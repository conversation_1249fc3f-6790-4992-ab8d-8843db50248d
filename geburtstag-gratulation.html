<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Herzlichen Glückwunsch zum Geburtstag!</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Playfair+Display:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Playfair Display', serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-x: hidden;
        }

        .card {
            background: white;
            border-radius: 25px;
            padding: 60px 50px;
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
            text-align: center;
            position: relative;
            animation: fadeInUp 1s ease-out;
        }

        .card::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            border-radius: 30px;
            z-index: -1;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .header {
            margin-bottom: 40px;
        }

        .title {
            font-family: 'Dancing Script', cursive;
            font-size: 3.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            animation: bounce 2s ease-in-out infinite;
        }

        .subtitle {
            font-size: 1.3rem;
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 30px;
        }

        .message {
            font-size: 1.2rem;
            line-height: 1.8;
            color: #34495e;
            margin-bottom: 40px;
            text-align: left;
        }

        .message p {
            margin-bottom: 20px;
        }

        .highlight {
            color: #e74c3c;
            font-weight: 700;
            font-style: italic;
        }

        .wishes {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #3498db;
        }

        .wishes h3 {
            font-family: 'Dancing Script', cursive;
            font-size: 2rem;
            color: #2980b9;
            margin-bottom: 20px;
        }

        .wishes ul {
            list-style: none;
            text-align: left;
        }

        .wishes li {
            margin-bottom: 12px;
            padding-left: 25px;
            position: relative;
            font-size: 1.1rem;
            color: #2c3e50;
        }

        .wishes li::before {
            content: '🌟';
            position: absolute;
            left: 0;
            animation: twinkle 2s ease-in-out infinite;
        }

        .signature {
            font-family: 'Dancing Script', cursive;
            font-size: 1.8rem;
            color: #8e44ad;
            margin-top: 40px;
            font-weight: 700;
        }

        .decorations {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .balloon {
            position: absolute;
            width: 30px;
            height: 40px;
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            animation: float 3s ease-in-out infinite;
        }

        .balloon:nth-child(1) {
            background: #ff6b6b;
            top: 10%;
            left: 5%;
            animation-delay: 0s;
        }

        .balloon:nth-child(2) {
            background: #4ecdc4;
            top: 20%;
            right: 5%;
            animation-delay: 1s;
        }

        .balloon:nth-child(3) {
            background: #45b7d1;
            bottom: 20%;
            left: 8%;
            animation-delay: 2s;
        }

        .balloon:nth-child(4) {
            background: #feca57;
            bottom: 10%;
            right: 8%;
            animation-delay: 0.5s;
        }

        .confetti {
            position: fixed;
            width: 10px;
            height: 10px;
            background: #ff6b6b;
            animation: confettiFall 3s linear infinite;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        @keyframes twinkle {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.5;
                transform: scale(1.2);
            }
        }

        @keyframes gradientShift {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        @keyframes confettiFall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        @media (max-width: 768px) {
            .card {
                padding: 40px 30px;
                margin: 10px;
            }
            
            .title {
                font-size: 2.5rem;
            }
            
            .message {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="decorations">
        <div class="balloon"></div>
        <div class="balloon"></div>
        <div class="balloon"></div>
        <div class="balloon"></div>
    </div>

    <div class="card">
        <div class="header">
            <h1 class="title">Herzlichen Glückwunsch!</h1>
            <p class="subtitle">Zum wundervollen Geburtstag</p>
        </div>

        <div class="message">
            <p>Liebe Леся,</p>

            <p>an diesem besonderen Tag möchte ich Dir von ganzem Herzen gratulieren! Du bist nicht nur eine <span class="highlight">wunderbare Schwester</span>, sondern auch eine <span class="highlight">inspirierende Lehrerin</span>, die das Leben so vieler Menschen bereichert.</p>
            
            <p>Deine Leidenschaft für die deutsche Sprache und Deine Fähigkeit, Wissen zu vermitteln, sind wahrhaft bewundernswert. Du öffnest Türen zu neuen Welten und schenkst Deinen Schülern die Magie der Sprache.</p>
            
            <p>Möge dieses neue Lebensjahr Dir <span class="highlight">Gesundheit, Glück und Erfüllung</span> bringen!</p>
        </div>

        <div class="wishes">
            <h3>Meine Wünsche für Dich:</h3>
            <ul>
                <li>Dass jeder Tag Dir neue Freude schenkt</li>
                <li>Dass Deine Träume in Erfüllung gehen</li>
                <li>Dass Du stets von lieben Menschen umgeben bist</li>
                <li>Dass Deine Arbeit Dir weiterhin so viel Sinn gibt</li>
                <li>Dass Gesundheit und Glück Deine treuen Begleiter sind</li>
                <li>Dass das Leben Dir noch viele schöne Überraschungen bereithält</li>
            </ul>
        </div>

        <div class="signature">
            Alles Liebe zu Deinem Ehrentag! 🎂✨
        </div>
    </div>

    <script>
        // Konfetti-Effekt
        function createConfetti() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#fd79a8'];
            
            for (let i = 0; i < 50; i++) {
                setTimeout(() => {
                    const confetti = document.createElement('div');
                    confetti.className = 'confetti';
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                    confetti.style.animationDelay = Math.random() * 3 + 's';
                    confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                    document.body.appendChild(confetti);
                    
                    setTimeout(() => {
                        confetti.remove();
                    }, 5000);
                }, i * 100);
            }
        }

        // Запускаем конфетти при загрузке
        window.addEventListener('load', () => {
            setTimeout(createConfetti, 1000);
            
            // Повторяем каждые 10 секунд
            setInterval(createConfetti, 10000);
        });

        // Дополнительное конфетти при клике
        document.addEventListener('click', createConfetti);
    </script>
</body>
</html>
