import React, { useState, useCallback } from 'react';
import { FileUpload } from './components/FileUpload';
import { OptimizationResults } from './components/OptimizationResults';
import { optimizeLottieFile } from './services/api';

interface OptimizationResult {
  optimized: any;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
}

function App() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<OptimizationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [originalFileName, setOriginalFileName] = useState<string>('');

  const handleFileUpload = useCallback(async (file: File) => {
    setIsLoading(true);
    setError(null);
    setResult(null);
    setOriginalFileName(file.name);

    try {
      const optimizationResult = await optimizeLottieFile(file);
      setResult(optimizationResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Произошла ошибка при оптимизации файла');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleReset = useCallback(() => {
    setResult(null);
    setError(null);
    setOriginalFileName('');
  }, []);

  return (
    <div className="container">
      <header className="header">
        <h1>Lottie Optimizer</h1>
        <p>Оптимизируйте ваши Lottie анимации с сохранением качества</p>
      </header>

      {!result && !isLoading && (
        <FileUpload onFileUpload={handleFileUpload} />
      )}

      {isLoading && (
        <div className="upload-area">
          <div className="loading">
            <div className="spinner"></div>
            <span>Оптимизируем ваш файл...</span>
          </div>
        </div>
      )}

      {error && (
        <div className="upload-area">
          <div className="error">
            <strong>Ошибка:</strong> {error}
          </div>
          <button className="btn btn-secondary" onClick={handleReset}>
            Попробовать снова
          </button>
        </div>
      )}

      {result && (
        <OptimizationResults 
          result={result} 
          fileName={originalFileName}
          onReset={handleReset}
        />
      )}
    </div>
  );
}

export default App;
