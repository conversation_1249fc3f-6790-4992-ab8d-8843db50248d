<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Optimize - Результаты оптимизации</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .summary {
            padding: 40px;
            background: #fff;
        }

        .summary h2 {
            font-size: 2rem;
            margin-bottom: 30px;
            color: #333;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .stat-card .emoji {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .stat-card .value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-card .label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .results-section {
            padding: 0 40px 40px;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 40px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .results-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }

        .results-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .results-table tr:hover {
            background: #f8f9fa;
        }

        .compression-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            color: white;
        }

        .compression-excellent { background: #28a745; }
        .compression-good { background: #17a2b8; }
        .compression-fair { background: #ffc107; color: #333; }

        .demo-section {
            padding: 40px;
            background: #f8f9fa;
        }

        .demo-section h2 {
            font-size: 2rem;
            margin-bottom: 30px;
            color: #333;
            text-align: center;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .demo-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }

        .demo-card h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .demo-card .demo-stats {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .lottie-container {
            width: 200px;
            height: 200px;
            margin: 0 auto;
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .lottie-container:hover {
            transform: scale(1.05);
        }

        .lottie-animation {
            width: 100%;
            height: 100%;
        }

        .lottie-controls {
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 5px;
            background: rgba(0,0,0,0.7);
            padding: 5px;
            border-radius: 15px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .lottie-container:hover .lottie-controls {
            opacity: 1;
        }

        .lottie-btn {
            background: white;
            border: none;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .lottie-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 0.9rem;
            flex-direction: column;
            gap: 10px;
        }

        .lottie-error {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #dc3545;
            font-size: 0.8rem;
            text-align: center;
            padding: 10px;
        }

        .download-section {
            padding: 40px;
            background: white;
            text-align: center;
        }

        .download-section h2 {
            font-size: 2rem;
            margin-bottom: 30px;
            color: #333;
        }

        .download-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            display: inline-block;
            text-decoration: none;
            transition: transform 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .download-card:hover {
            transform: translateY(-5px);
            text-decoration: none;
            color: white;
        }

        .download-card .emoji {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .download-card h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .download-card p {
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .download-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .download-btn {
            background: rgba(255,255,255,0.2);
            border: 2px solid white;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: white;
            color: #667eea;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .header, .summary, .results-section, .demo-section, .download-section {
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .results-table {
                font-size: 0.9rem;
            }
            
            .results-table th,
            .results-table td {
                padding: 10px 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎬 Lottie Optimize</h1>
            <p>Результаты оптимизации 45 файлов</p>
            <div style="margin-top: 20px;">
                <a href="index.html" style="color: white; text-decoration: none; background: rgba(255,255,255,0.2); padding: 10px 20px; border-radius: 25px; border: 2px solid white; transition: all 0.3s ease;"
                   onmouseover="this.style.background='white'; this.style.color='#667eea';"
                   onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.color='white';">
                    ← Вернуться к оптимизатору
                </a>
            </div>
        </div>

        <!-- Summary Statistics -->
        <div class="summary">
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="emoji">📏</span>
                    <div class="value" id="totalOriginal">0 MB</div>
                    <div class="label">Исходный размер</div>
                </div>
                <div class="stat-card">
                    <span class="emoji">💾</span>
                    <div class="value" id="totalOptimized">0 MB</div>
                    <div class="label">После оптимизации</div>
                </div>
                <div class="stat-card">
                    <span class="emoji">💰</span>
                    <div class="value" id="totalSaved">0 MB</div>
                    <div class="label">Сэкономлено</div>
                </div>
                <div class="stat-card">
                    <span class="emoji">🚀</span>
                    <div class="value" id="averageCompression">0%</div>
                    <div class="label">Степень сжатия</div>
                </div>
            </div>
        </div>

        <!-- Detailed Results -->
        <div class="results-section">
            <h2>📊 Детальные результаты</h2>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>Файл</th>
                        <th>Исходный размер</th>
                        <th>Оптимизированный</th>
                        <th>Сэкономлено</th>
                        <th>Сжатие</th>
                    </tr>
                </thead>
                <tbody id="resultsTableBody">
                    <!-- Results will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Demo Section -->
        <div class="demo-section">
            <h2>🎬 Демонстрация оптимизированных анимаций</h2>
            <div class="demo-grid" id="demoGrid">
                <!-- Demo cards will be populated by JavaScript -->
            </div>
        </div>

        <!-- Download Section -->
        <div class="download-section">
            <h2>📦 Скачать файлы</h2>
            <a href="#" class="download-card" id="downloadAll">
                <span class="emoji">📁</span>
                <h3>Все оптимизированные файлы</h3>
                <p>Архив содержит все 45 оптимизированных Lottie файла</p>
                <div class="download-stats">
                    <span>📊 45 файлов</span>
                    <span id="downloadSize">💾 0 MB</span>
                    <span id="downloadCompression">🚀 0% сжатие</span>
                </div>
                <span class="download-btn">📥 Скачать архив</span>
            </a>
        </div>
    </div>

    <script>
        // Real-time optimization results - will be populated by processing actual files
        let optimizationResults = [];

        // LottieOptimizer class for real-time processing
        class LottieOptimizer {
            constructor() {
                this.qualityThreshold = 0.6;
            }

            async optimizeLottie(lottieData) {
                const originalJson = JSON.stringify(lottieData);
                const originalSize = new Blob([originalJson]).size;

                const optimized = JSON.parse(originalJson);

                await this.optimizeAssets(optimized.assets || []);
                this.optimizeJsonStructure(optimized);
                this.roundNumbers(optimized);

                const optimizedJson = JSON.stringify(optimized);
                const optimizedSize = new Blob([optimizedJson]).size;
                const compressionRatio = (originalSize - optimizedSize) / originalSize;

                return {
                    optimized,
                    originalSize,
                    optimizedSize,
                    compressionRatio
                };
            }

            async optimizeAssets(assets) {
                for (const asset of assets) {
                    if (asset.p && this.isBase64Image(asset.p)) {
                        try {
                            asset.p = await this.compressBase64Image(asset.p);
                        } catch (error) {
                            console.warn('Failed to compress image:', error);
                        }
                    }
                }
            }

            isBase64Image(str) {
                return str.startsWith('data:image/');
            }

            async compressBase64Image(base64) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        if (!ctx) {
                            reject(new Error('Cannot get canvas context'));
                            return;
                        }

                        const maxWidth = 1024;
                        const maxHeight = 1024;
                        let { width, height } = img;

                        if (width > maxWidth || height > maxHeight) {
                            const ratio = Math.min(maxWidth / width, maxHeight / height);
                            width *= ratio;
                            height *= ratio;
                        }

                        canvas.width = width;
                        canvas.height = height;
                        ctx.drawImage(img, 0, 0, width, height);

                        const compressedBase64 = canvas.toDataURL('image/jpeg', 0.8);
                        resolve(compressedBase64);
                    };

                    img.onerror = () => reject(new Error('Failed to load image'));
                    img.src = base64;
                });
            }

            optimizeJsonStructure(lottie) {
                this.removeUnusedProperties(lottie);
                if (lottie.layers) {
                    this.optimizeLayers(lottie.layers);
                }
            }

            removeUnusedProperties(obj) {
                const unnecessaryProps = ['nm', 'mn', 'hd'];

                if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
                    for (const prop of unnecessaryProps) {
                        if (prop in obj) {
                            delete obj[prop];
                        }
                    }

                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            this.removeUnusedProperties(obj[key]);
                        }
                    }
                } else if (Array.isArray(obj)) {
                    obj.forEach(item => this.removeUnusedProperties(item));
                }
            }

            optimizeLayers(layers) {
                if (!Array.isArray(layers)) return;

                layers.forEach(layer => {
                    if (layer.hd === true) return;

                    if (layer.ks) {
                        this.optimizeKeyframes(layer.ks);
                    }

                    if (layer.layers) {
                        this.optimizeLayers(layer.layers);
                    }
                });
            }

            optimizeKeyframes(ks) {
                if (typeof ks === 'object' && ks !== null) {
                    for (const key in ks) {
                        if (ks[key] && typeof ks[key] === 'object') {
                            if (ks[key].k && Array.isArray(ks[key].k)) {
                                ks[key].k = this.removeDuplicateKeyframes(ks[key].k);
                            }
                            this.optimizeKeyframes(ks[key]);
                        }
                    }
                }
            }

            removeDuplicateKeyframes(keyframes) {
                const unique = [];
                let lastKeyframe = null;

                for (const kf of keyframes) {
                    if (!lastKeyframe || !this.areKeyframesEqual(kf, lastKeyframe)) {
                        unique.push(kf);
                        lastKeyframe = kf;
                    }
                }

                return unique;
            }

            areKeyframesEqual(kf1, kf2) {
                return JSON.stringify(kf1.s) === JSON.stringify(kf2.s) &&
                       JSON.stringify(kf1.e) === JSON.stringify(kf2.e);
            }

            roundNumbers(obj, precision = 3) {
                if (typeof obj === 'number') {
                    return Math.round(obj * Math.pow(10, precision)) / Math.pow(10, precision);
                }

                if (Array.isArray(obj)) {
                    for (let i = 0; i < obj.length; i++) {
                        if (typeof obj[i] === 'number') {
                            obj[i] = Math.round(obj[i] * Math.pow(10, precision)) / Math.pow(10, precision);
                        } else {
                            this.roundNumbers(obj[i], precision);
                        }
                    }
                } else if (typeof obj === 'object' && obj !== null) {
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            if (typeof obj[key] === 'number') {
                                obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
                            } else {
                                this.roundNumbers(obj[key], precision);
                            }
                        }
                    }
                }
            }
        }

        const optimizer = new LottieOptimizer();

        function formatFileSize(mb) {
            if (mb >= 1) {
                return mb.toFixed(1) + ' MB';
            } else {
                return (mb * 1024).toFixed(1) + ' KB';
            }
        }

        function getCompressionClass(compression) {
            if (compression >= 90) return 'compression-excellent';
            if (compression >= 80) return 'compression-good';
            return 'compression-fair';
        }

        // Process all Lottie files and generate real optimization results
        async function processAllFiles() {
            const lottieFiles = [];

            // Generate file list (1.json to 45.json)
            for (let i = 1; i <= 45; i++) {
                lottieFiles.push(`mob/output/${i}.json`);
            }

            const tableBody = document.getElementById('resultsTableBody');
            const demoGrid = document.getElementById('demoGrid');

            // Show loading state
            tableBody.innerHTML = '<tr><td colspan="5" style="text-align: center; padding: 40px;">🔄 Обрабатываем файлы...</td></tr>';
            demoGrid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 40px;">🔄 Генерируем демонстрацию...</div>';

            let totalOriginal = 0;
            let totalOptimized = 0;
            let processedCount = 0;

            // Clear previous results
            optimizationResults = [];
            tableBody.innerHTML = '';
            demoGrid.innerHTML = '';

            for (const fileName of lottieFiles) {
                try {
                    const response = await fetch(fileName);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const lottieData = await response.json();
                    const result = await optimizer.optimizeLottie(lottieData);

                    const originalMB = result.originalSize / (1024 * 1024);
                    const optimizedMB = result.optimizedSize / (1024 * 1024);
                    const compression = result.compressionRatio * 100;

                    const optimizationResult = {
                        file: fileName.split('/').pop(),
                        original: originalMB,
                        optimized: optimizedMB,
                        compression: compression,
                        optimizedData: result.optimized
                    };

                    optimizationResults.push(optimizationResult);
                    totalOriginal += originalMB;
                    totalOptimized += optimizedMB;
                    processedCount++;

                    // Add table row
                    const row = document.createElement('tr');
                    const saved = originalMB - optimizedMB;

                    row.innerHTML = `
                        <td><strong>${optimizationResult.file}</strong></td>
                        <td>${formatFileSize(originalMB)}</td>
                        <td>${formatFileSize(optimizedMB)}</td>
                        <td>${formatFileSize(saved)}</td>
                        <td><span class="compression-badge ${getCompressionClass(compression)}">${compression.toFixed(1)}%</span></td>
                    `;
                    tableBody.appendChild(row);

                    // Add demo card with real Lottie animation
                    const demoCard = document.createElement('div');
                    demoCard.className = 'demo-card';
                    const animationId = `lottie-${optimizationResults.length}`;
                    demoCard.innerHTML = `
                        <h3>${optimizationResult.file}</h3>
                        <div class="demo-stats">
                            Сжатие: ${compression.toFixed(1)}% • ${formatFileSize(optimizedMB)}
                        </div>
                        <div class="lottie-container" id="${animationId}">
                            <div class="lottie-loading">
                                <div>🎬</div>
                                <div>Загружаем анимацию...</div>
                            </div>
                        </div>
                        <button onclick="downloadSingle('${optimizationResult.file}', ${optimizationResults.length})"
                                style="margin-top: 10px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            📥 Скачать
                        </button>
                    `;
                    demoGrid.appendChild(demoCard);

                    // Load Lottie animation
                    loadLottieAnimation(animationId, result.optimized);

                    // Update progress
                    const progress = (processedCount / lottieFiles.length) * 100;
                    document.title = `Lottie Optimize - ${progress.toFixed(0)}% готово`;

                } catch (error) {
                    console.error(`Error processing ${fileName}:`, error);

                    // Add error row
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td><strong>${fileName.split('/').pop()}</strong></td>
                        <td colspan="4" style="color: #dc3545;">Ошибка: ${error.message}</td>
                    `;
                    tableBody.appendChild(row);
                }

                // Small delay to prevent blocking the UI
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Update summary statistics
            const totalSaved = totalOriginal - totalOptimized;
            const averageCompression = optimizationResults.length > 0 ? (totalSaved / totalOriginal) * 100 : 0;

            document.getElementById('totalOriginal').textContent = formatFileSize(totalOriginal);
            document.getElementById('totalOptimized').textContent = formatFileSize(totalOptimized);
            document.getElementById('totalSaved').textContent = formatFileSize(totalSaved);
            document.getElementById('averageCompression').textContent = averageCompression.toFixed(1) + '%';

            // Update download section
            document.getElementById('downloadSize').textContent = '💾 ' + formatFileSize(totalOptimized);
            document.getElementById('downloadCompression').textContent = '🚀 ' + averageCompression.toFixed(1) + '% сжатие';

            // Update header
            document.querySelector('.header p').textContent = `Результаты оптимизации ${optimizationResults.length} файлов`;
            document.title = 'Lottie Optimize - Результаты оптимизации';
        }

        function downloadSingle(fileName, index) {
            const result = optimizationResults[index];
            if (result && result.optimizedData) {
                const dataStr = JSON.stringify(result.optimizedData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = fileName.replace('.json', '_optimized.json');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }
        }

        function downloadAllOptimized() {
            if (optimizationResults.length === 0) {
                alert('Нет оптимизированных файлов для скачивания');
                return;
            }

            // Create a simple archive simulation by downloading files one by one
            let downloadCount = 0;
            const totalFiles = optimizationResults.length;

            optimizationResults.forEach((result, index) => {
                setTimeout(() => {
                    downloadSingle(result.file, index);
                    downloadCount++;

                    if (downloadCount === totalFiles) {
                        alert(`Скачивание завершено! Загружено ${totalFiles} оптимизированных файлов.`);
                    }
                }, index * 500); // Delay between downloads
            });
        }

        // Lottie animation loader
        const lottieAnimations = {};

        function loadLottieAnimation(containerId, animationData) {
            try {
                const container = document.getElementById(containerId);
                if (!container) return;

                // Clear loading state
                container.innerHTML = `
                    <div class="lottie-animation" id="${containerId}-animation"></div>
                    <div class="lottie-controls">
                        <button class="lottie-btn" onclick="toggleAnimation('${containerId}')" title="Play/Pause">⏯️</button>
                        <button class="lottie-btn" onclick="restartAnimation('${containerId}')" title="Restart">🔄</button>
                    </div>
                `;

                // Load Lottie animation
                const animation = lottie.loadAnimation({
                    container: document.getElementById(`${containerId}-animation`),
                    renderer: 'svg',
                    loop: true,
                    autoplay: true,
                    animationData: animationData
                });

                lottieAnimations[containerId] = animation;

                // Handle animation errors
                animation.addEventListener('data_failed', () => {
                    container.innerHTML = `
                        <div class="lottie-error">
                            ❌ Ошибка загрузки анимации
                        </div>
                    `;
                });

            } catch (error) {
                console.error('Error loading Lottie animation:', error);
                const container = document.getElementById(containerId);
                if (container) {
                    container.innerHTML = `
                        <div class="lottie-error">
                            ❌ Ошибка: ${error.message}
                        </div>
                    `;
                }
            }
        }

        function toggleAnimation(containerId) {
            const animation = lottieAnimations[containerId];
            if (animation) {
                if (animation.isPaused) {
                    animation.play();
                } else {
                    animation.pause();
                }
            }
        }

        function restartAnimation(containerId) {
            const animation = lottieAnimations[containerId];
            if (animation) {
                animation.goToAndPlay(0);
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Start processing files automatically
            processAllFiles();

            // Add download functionality
            document.getElementById('downloadAll').addEventListener('click', function(e) {
                e.preventDefault();
                downloadAllOptimized();
            });
        });
    </script>
</body>
</html>
