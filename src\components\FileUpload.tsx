import React, { useCallback, useState } from 'react';

interface FileUploadProps {
  onFileUpload: (file: File) => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onFileUpload }) => {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    const lottieFile = files.find(file => 
      file.type === 'application/json' || 
      file.name.toLowerCase().endsWith('.json')
    );

    if (lottieFile) {
      onFileUpload(lottieFile);
    } else {
      alert('Пожалуйста, выберите JSON файл с Lottie анимацией');
    }
  }, [onFileUpload]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onFileUpload(file);
    }
  }, [onFileUpload]);

  const handleClick = useCallback(() => {
    const input = document.getElementById('file-input') as HTMLInputElement;
    input?.click();
  }, []);

  return (
    <div className="upload-area">
      <div 
        className={`dropzone ${isDragOver ? 'dragover' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <div className="dropzone-content">
          <div className="upload-icon">
            📁
          </div>
          <h3>Перетащите Lottie файл сюда</h3>
          <p>или нажмите для выбора файла</p>
          <p style={{ fontSize: '0.9rem', color: '#999' }}>
            Поддерживаются файлы .json
          </p>
        </div>
      </div>
      
      <input
        id="file-input"
        type="file"
        accept=".json,application/json"
        onChange={handleFileSelect}
        className="file-input"
      />
    </div>
  );
};
