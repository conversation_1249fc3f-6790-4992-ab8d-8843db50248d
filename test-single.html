<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест оптимизации одного файла</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .loading {
            display: none;
            color: #007bff;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Тест оптимизации Lottie файла</h1>
        
        <button class="btn" onclick="testFile('mob/output/1.json')">Тестировать 1.json</button>
        <button class="btn" onclick="testFile('mob/output/24.json')">Тестировать 24.json (большой)</button>
        <button class="btn" onclick="testAllFiles()">Тестировать все файлы</button>
        
        <div class="loading" id="loading">🔄 Обрабатываем файл...</div>
        <div id="results"></div>
    </div>

    <script>
        // LottieOptimizer class
        class LottieOptimizer {
            constructor() {
                this.qualityThreshold = 0.6;
            }

            async optimizeLottie(lottieData) {
                const originalJson = JSON.stringify(lottieData);
                const originalSize = new Blob([originalJson]).size;

                const optimized = JSON.parse(originalJson);

                await this.optimizeAssets(optimized.assets || []);
                this.optimizeJsonStructure(optimized);
                this.roundNumbers(optimized);

                const optimizedJson = JSON.stringify(optimized);
                const optimizedSize = new Blob([optimizedJson]).size;
                const compressionRatio = (originalSize - optimizedSize) / originalSize;

                return {
                    optimized,
                    originalSize,
                    optimizedSize,
                    compressionRatio
                };
            }

            async optimizeAssets(assets) {
                for (const asset of assets) {
                    if (asset.p && this.isBase64Image(asset.p)) {
                        try {
                            asset.p = await this.compressBase64Image(asset.p);
                        } catch (error) {
                            console.warn('Failed to compress image:', error);
                        }
                    }
                }
            }

            isBase64Image(str) {
                return str.startsWith('data:image/');
            }

            async compressBase64Image(base64) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        
                        if (!ctx) {
                            reject(new Error('Cannot get canvas context'));
                            return;
                        }

                        const maxWidth = 1024;
                        const maxHeight = 1024;
                        let { width, height } = img;

                        if (width > maxWidth || height > maxHeight) {
                            const ratio = Math.min(maxWidth / width, maxHeight / height);
                            width *= ratio;
                            height *= ratio;
                        }

                        canvas.width = width;
                        canvas.height = height;
                        ctx.drawImage(img, 0, 0, width, height);

                        const compressedBase64 = canvas.toDataURL('image/jpeg', 0.8);
                        resolve(compressedBase64);
                    };

                    img.onerror = () => reject(new Error('Failed to load image'));
                    img.src = base64;
                });
            }

            optimizeJsonStructure(lottie) {
                this.removeUnusedProperties(lottie);
                if (lottie.layers) {
                    this.optimizeLayers(lottie.layers);
                }
            }

            removeUnusedProperties(obj) {
                const unnecessaryProps = ['nm', 'mn', 'hd'];
                
                if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
                    for (const prop of unnecessaryProps) {
                        if (prop in obj) {
                            delete obj[prop];
                        }
                    }

                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            this.removeUnusedProperties(obj[key]);
                        }
                    }
                } else if (Array.isArray(obj)) {
                    obj.forEach(item => this.removeUnusedProperties(item));
                }
            }

            optimizeLayers(layers) {
                if (!Array.isArray(layers)) return;

                layers.forEach(layer => {
                    if (layer.hd === true) return;

                    if (layer.ks) {
                        this.optimizeKeyframes(layer.ks);
                    }

                    if (layer.layers) {
                        this.optimizeLayers(layer.layers);
                    }
                });
            }

            optimizeKeyframes(ks) {
                if (typeof ks === 'object' && ks !== null) {
                    for (const key in ks) {
                        if (ks[key] && typeof ks[key] === 'object') {
                            if (ks[key].k && Array.isArray(ks[key].k)) {
                                ks[key].k = this.removeDuplicateKeyframes(ks[key].k);
                            }
                            this.optimizeKeyframes(ks[key]);
                        }
                    }
                }
            }

            removeDuplicateKeyframes(keyframes) {
                const unique = [];
                let lastKeyframe = null;

                for (const kf of keyframes) {
                    if (!lastKeyframe || !this.areKeyframesEqual(kf, lastKeyframe)) {
                        unique.push(kf);
                        lastKeyframe = kf;
                    }
                }

                return unique;
            }

            areKeyframesEqual(kf1, kf2) {
                return JSON.stringify(kf1.s) === JSON.stringify(kf2.s) &&
                       JSON.stringify(kf1.e) === JSON.stringify(kf2.e);
            }

            roundNumbers(obj, precision = 3) {
                if (typeof obj === 'number') {
                    return Math.round(obj * Math.pow(10, precision)) / Math.pow(10, precision);
                }

                if (Array.isArray(obj)) {
                    for (let i = 0; i < obj.length; i++) {
                        if (typeof obj[i] === 'number') {
                            obj[i] = Math.round(obj[i] * Math.pow(10, precision)) / Math.pow(10, precision);
                        } else {
                            this.roundNumbers(obj[i], precision);
                        }
                    }
                } else if (typeof obj === 'object' && obj !== null) {
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            if (typeof obj[key] === 'number') {
                                obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
                            } else {
                                this.roundNumbers(obj[key], precision);
                            }
                        }
                    }
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        }

        const optimizer = new LottieOptimizer();

        async function testFile(fileName) {
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            
            loading.style.display = 'block';
            
            try {
                console.log(`Загружаем файл: ${fileName}`);
                const response = await fetch(fileName);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const lottieData = await response.json();
                console.log('Файл загружен, начинаем оптимизацию...');
                
                const startTime = performance.now();
                const result = await optimizer.optimizeLottie(lottieData);
                const endTime = performance.now();
                
                const compressionPercent = Math.round(result.compressionRatio * 100);
                const isGoodCompression = compressionPercent >= 60;
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result';
                resultDiv.innerHTML = `
                    <h3>${fileName}</h3>
                    <p><strong>Исходный размер:</strong> ${optimizer.formatFileSize(result.originalSize)}</p>
                    <p><strong>Оптимизированный размер:</strong> ${optimizer.formatFileSize(result.optimizedSize)}</p>
                    <p><strong>Сжатие:</strong> <span style="color: ${isGoodCompression ? 'green' : 'orange'}">${compressionPercent}%</span></p>
                    <p><strong>Сэкономлено:</strong> ${optimizer.formatFileSize(result.originalSize - result.optimizedSize)}</p>
                    <p><strong>Время обработки:</strong> ${(endTime - startTime).toFixed(2)} мс</p>
                    <button class="btn" onclick="downloadOptimized('${fileName}', ${JSON.stringify(result.optimized).replace(/"/g, '&quot;')})">
                        Скачать оптимизированный
                    </button>
                `;
                
                results.appendChild(resultDiv);
                
                if (isGoodCompression) {
                    showMessage('success', `Файл ${fileName} успешно оптимизирован с сжатием ${compressionPercent}%!`);
                } else {
                    showMessage('error', `Внимание: сжатие файла ${fileName} составляет только ${compressionPercent}%, что ниже порога в 60%.`);
                }
                
            } catch (error) {
                console.error(`Ошибка при обработке ${fileName}:`, error);
                showMessage('error', `Ошибка при обработке ${fileName}: ${error.message}`);
            } finally {
                loading.style.display = 'none';
            }
        }

        async function testAllFiles() {
            const files = [];
            for (let i = 1; i <= 45; i++) {
                files.push(`mob/output/${i}.json`);
            }
            
            for (const file of files) {
                await testFile(file);
                // Небольшая задержка между файлами
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        function downloadOptimized(fileName, optimizedData) {
            const dataStr = JSON.stringify(optimizedData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName.replace('.json', '_optimized.json').replace('mob/output/', '');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        function showMessage(type, message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            document.getElementById('results').appendChild(messageDiv);
            
            // Автоматически скрыть сообщение через 5 секунд
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
