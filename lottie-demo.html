<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Animations Demo - Быстрая загрузка</title>
    
    <!-- Preload критически важных ресурсов -->
    <link rel="preload" href="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js" as="script">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .performance-info {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .performance-info h3 {
            margin-bottom: 15px;
            color: #fff;
        }

        .performance-info ul {
            color: rgba(255,255,255,0.9);
            list-style: none;
            padding-left: 0;
        }

        .performance-info li {
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }

        .performance-info li::before {
            content: '⚡';
            position: absolute;
            left: 0;
        }

        .animations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .animation-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .animation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .animation-container {
            width: 100%;
            height: 300px;
            border-radius: 15px;
            overflow: hidden;
            background: #f8f9fa;
            position: relative;
            margin-bottom: 20px;
        }

        .animation-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            color: #999;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .animation-placeholder:hover {
            background: linear-gradient(45deg, #e0e0e0, #d0d0d0);
        }

        .animation-placeholder.loading {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .animation-info {
            text-align: center;
        }

        .animation-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .animation-description {
            color: #666;
            font-size: 0.95rem;
            margin-bottom: 15px;
        }

        .animation-stats {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #999;
            margin-bottom: 15px;
        }

        .animation-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .performance-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .size-info {
            font-size: 0.8rem;
            color: #28a745;
            font-weight: 600;
        }

        /* Скрытый lottie-player до загрузки */
        lottie-player {
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        lottie-player.loaded {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .animations-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .animation-container {
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎬 Lottie Animations Demo</h1>
            <p>Демонстрация оптимизированных анимаций с быстрой загрузкой</p>
            
            <div class="performance-info">
                <h3>🚀 Оптимизации для скорости:</h3>
                <ul>
                    <li>Lazy Loading - анимации загружаются только при просмотре</li>
                    <li>CDN доставка - файлы загружаются с ближайшего сервера</li>
                    <li>Preload критических ресурсов</li>
                    <li>Сжатие изображений до 712×1266</li>
                    <li>Оптимизация JSON структуры</li>
                    <li>Intersection Observer API для производительности</li>
                </ul>
            </div>
        </header>

        <div class="animations-grid" id="animationsGrid">
            <!-- Анимации будут добавлены через JavaScript -->
        </div>
    </div>

    <!-- Загружаем Lottie Player с CDN -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js" defer></script>
    
    <script>
        class LottieDemo {
            constructor() {
                this.animations = [
                    {
                        id: 'animation1',
                        title: 'Анимация 1',
                        description: 'Описание первой анимации',
                        file: 'animations/1.json', // Замените на ваши файлы
                        size: '45 KB'
                    },
                    {
                        id: 'animation2', 
                        title: 'Анимация 2',
                        description: 'Описание второй анимации',
                        file: 'animations/2.json',
                        size: '38 KB'
                    },
                    {
                        id: 'animation3',
                        title: 'Анимация 3', 
                        description: 'Описание третьей анимации',
                        file: 'animations/3.json',
                        size: '52 KB'
                    },
                    // Добавьте больше анимаций по необходимости
                ];
                
                this.observer = null;
                this.init();
            }

            init() {
                this.createAnimationCards();
                this.setupIntersectionObserver();
            }

            createAnimationCards() {
                const grid = document.getElementById('animationsGrid');
                
                this.animations.forEach(animation => {
                    const card = this.createAnimationCard(animation);
                    grid.appendChild(card);
                });
            }

            createAnimationCard(animation) {
                const card = document.createElement('div');
                card.className = 'animation-card';
                card.innerHTML = `
                    <div class="performance-badge">Оптимизировано</div>
                    <div class="animation-container" data-animation="${animation.file}">
                        <div class="animation-placeholder" onclick="lottieDemo.loadAnimation('${animation.id}', '${animation.file}')">
                            <span>🎬 Нажмите для загрузки</span>
                        </div>
                    </div>
                    <div class="animation-info">
                        <div class="animation-title">${animation.title}</div>
                        <div class="animation-description">${animation.description}</div>
                        <div class="animation-stats">
                            <span>Размер: <span class="size-info">${animation.size}</span></span>
                            <span>Формат: JSON</span>
                        </div>
                        <div class="animation-controls">
                            <button class="btn btn-primary" onclick="lottieDemo.playAnimation('${animation.id}')">▶️ Играть</button>
                            <button class="btn btn-secondary" onclick="lottieDemo.pauseAnimation('${animation.id}')">⏸️ Пауза</button>
                            <button class="btn btn-secondary" onclick="lottieDemo.restartAnimation('${animation.id}')">🔄 Сначала</button>
                        </div>
                    </div>
                `;
                card.id = animation.id;
                return card;
            }

            setupIntersectionObserver() {
                // Автоматическая загрузка при появлении в viewport
                this.observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const container = entry.target.querySelector('.animation-container');
                            const animationFile = container.dataset.animation;
                            const animationId = entry.target.id;
                            
                            // Загружаем только если еще не загружено
                            if (!container.querySelector('lottie-player')) {
                                setTimeout(() => {
                                    this.loadAnimation(animationId, animationFile);
                                }, 500); // Небольшая задержка для плавности
                            }
                        }
                    });
                }, {
                    rootMargin: '50px' // Загружаем за 50px до появления
                });

                // Наблюдаем за всеми карточками
                document.querySelectorAll('.animation-card').forEach(card => {
                    this.observer.observe(card);
                });
            }

            async loadAnimation(animationId, animationFile) {
                const container = document.querySelector(`#${animationId} .animation-container`);
                const placeholder = container.querySelector('.animation-placeholder');
                
                // Показываем загрузку
                placeholder.className = 'animation-placeholder loading';
                placeholder.innerHTML = '<div class="loading-spinner"></div><span>Загружаем...</span>';

                try {
                    // Создаем lottie-player
                    const player = document.createElement('lottie-player');
                    player.setAttribute('src', animationFile);
                    player.setAttribute('background', 'transparent');
                    player.setAttribute('speed', '1');
                    player.setAttribute('loop', 'true');
                    player.setAttribute('autoplay', 'true');
                    
                    // Ждем загрузки
                    await new Promise((resolve, reject) => {
                        player.addEventListener('ready', resolve);
                        player.addEventListener('error', reject);
                        
                        // Таймаут на случай проблем
                        setTimeout(() => reject(new Error('Timeout')), 10000);
                    });

                    // Заменяем placeholder на player
                    container.innerHTML = '';
                    container.appendChild(player);
                    
                    // Плавное появление
                    setTimeout(() => {
                        player.classList.add('loaded');
                    }, 100);

                    console.log(`Анимация ${animationId} успешно загружена`);

                } catch (error) {
                    console.error(`Ошибка загрузки анимации ${animationId}:`, error);
                    placeholder.className = 'animation-placeholder';
                    placeholder.innerHTML = '❌ Ошибка загрузки<br><small>Проверьте путь к файлу</small>';
                }
            }

            playAnimation(animationId) {
                const player = document.querySelector(`#${animationId} lottie-player`);
                if (player) player.play();
            }

            pauseAnimation(animationId) {
                const player = document.querySelector(`#${animationId} lottie-player`);
                if (player) player.pause();
            }

            restartAnimation(animationId) {
                const player = document.querySelector(`#${animationId} lottie-player`);
                if (player) {
                    player.stop();
                    player.play();
                }
            }
        }

        // Инициализация после загрузки DOM
        let lottieDemo;
        document.addEventListener('DOMContentLoaded', () => {
            lottieDemo = new LottieDemo();
        });
    </script>
</body>
</html>
