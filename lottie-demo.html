<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Animations Demo - Быстрая загрузка</title>
    
    <!-- Preload критически важных ресурсов -->
    <link rel="preload" href="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js" as="script">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">

    <!-- Preload только приоритетных анимаций (preload: true) -->
    <link rel="preload" href="animations/1.json" as="fetch" crossorigin>
    <link rel="preload" href="animations/2.json" as="fetch" crossorigin>
    <!-- НЕ добавляйте сюда lazy анимации (preload: false) -->
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .performance-info {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .performance-info h3 {
            margin-bottom: 15px;
            color: #fff;
        }

        .performance-info ul {
            color: rgba(255,255,255,0.9);
            list-style: none;
            padding-left: 0;
        }

        .performance-info li {
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }

        .performance-info li::before {
            content: '⚡';
            position: absolute;
            left: 0;
        }

        .animations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .animation-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .animation-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .animation-container {
            width: 100%;
            height: 300px;
            border-radius: 15px;
            overflow: hidden;
            background: #f8f9fa;
            position: relative;
            margin-bottom: 20px;
        }

        .animation-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            color: #999;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .animation-placeholder:hover {
            background: linear-gradient(45deg, #e0e0e0, #d0d0d0);
        }

        .animation-placeholder.loading {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .animation-info {
            text-align: center;
        }

        .animation-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .animation-description {
            color: #666;
            font-size: 0.95rem;
            margin-bottom: 15px;
        }

        .animation-stats {
            display: flex;
            justify-content: space-between;
            font-size: 0.85rem;
            color: #999;
            margin-bottom: 15px;
        }

        .animation-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .code-explanation {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            border-left: 4px solid #667eea;
        }

        .code-explanation h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 12px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            overflow-x: auto;
            margin: 10px 0;
        }

        .code-comment {
            color: #68d391;
        }

        .code-key {
            color: #fbb6ce;
        }

        .code-value {
            color: #90cdf4;
        }

        .code-string {
            color: #faf089;
        }

        .explanation-text {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .performance-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .size-info {
            font-size: 0.8rem;
            color: #28a745;
            font-weight: 600;
        }

        /* Скрытый lottie-player до загрузки */
        lottie-player {
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        lottie-player.loaded {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .animations-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .animation-container {
                height: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎬 Lottie Animations Demo</h1>
            <p>Демонстрация оптимизированных анимаций с быстрой загрузкой</p>
            
            <div class="performance-info">
                <h3>🚀 Умные оптимизации для скорости:</h3>
                <ul>
                    <li>Селективная предзагрузка - только важные анимации загружаются сразу</li>
                    <li>Lazy loading - остальные анимации по требованию</li>
                    <li>CDN доставка - файлы загружаются с ближайшего сервера</li>
                    <li>Приоритизация - высокий/низкий приоритет загрузки</li>
                    <li>Кеширование в браузере для мгновенных повторных загрузок</li>
                    <li>Intersection Observer для эффективного отслеживания</li>
                </ul>
            </div>
        </header>

        <div class="control-panel" style="background: rgba(255,255,255,0.1); border-radius: 15px; padding: 20px; margin-bottom: 30px; backdrop-filter: blur(10px);">
            <h3 style="color: white; margin-bottom: 15px;">⚙️ Управление загрузкой</h3>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; justify-content: center;">
                <button class="btn btn-primary" onclick="lottieDemo.loadAllLazy()">
                    🚀 Загрузить все сейчас
                </button>
                <button class="btn btn-secondary" onclick="lottieDemo.showLoadingStats()">
                    📊 Статистика загрузки
                </button>
                <button class="btn btn-secondary" onclick="lottieDemo.preloadAllRemaining()">
                    📦 Предзагрузить оставшиеся
                </button>
            </div>
        </div>

        <div class="animations-grid" id="animationsGrid">
            <!-- Анимации будут добавлены через JavaScript -->
        </div>
    </div>

    <!-- Загружаем Lottie Player с CDN -->
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js" defer></script>
    
    <script>
        class LottieDemo {
            constructor() {
                this.animations = [
                    {
                        id: 'animation1',
                        title: 'Анимация 1 (Приоритетная)',
                        description: 'Загружается сразу при открытии страницы',
                        file: 'animations/1.json',
                        size: '45 KB',
                        preload: true, // Загружать сразу
                        priority: 'high' // Высокий приоритет
                    },
                    {
                        id: 'animation2',
                        title: 'Анимация 2 (Приоритетная)',
                        description: 'Также загружается сразу',
                        file: 'animations/2.json',
                        size: '38 KB',
                        preload: true, // Загружать сразу
                        priority: 'high'
                    },
                    {
                        id: 'animation3',
                        title: 'Анимация 3 (По требованию)',
                        description: 'Загружается только при клике или скролле',
                        file: 'animations/3.json',
                        size: '52 KB',
                        preload: false, // Загружать по требованию
                        priority: 'low'
                    },
                    {
                        id: 'animation4',
                        title: 'Анимация 4 (По требованию)',
                        description: 'Тяжелая анимация - загружается по требованию',
                        file: 'animations/4.json',
                        size: '120 KB',
                        preload: false, // Загружать по требованию
                        priority: 'low'
                    },
                    // Добавьте больше анимаций по необходимости
                ];
                
                this.observer = null;
                this.init();
            }

            init() {
                this.createAnimationCards();
                this.preloadAllAnimations();
            }

            createAnimationCards() {
                const grid = document.getElementById('animationsGrid');
                
                this.animations.forEach(animation => {
                    const card = this.createAnimationCard(animation);
                    grid.appendChild(card);
                });
            }

            createAnimationCard(animation) {
                const card = document.createElement('div');
                card.className = 'animation-card';

                // Определяем badge и placeholder в зависимости от настроек
                const badge = animation.preload ?
                    `<div class="performance-badge" style="background: #28a745;">Автозагрузка</div>` :
                    `<div class="performance-badge" style="background: #17a2b8;">По требованию</div>`;

                const placeholder = animation.preload ?
                    `<div class="animation-placeholder loading">
                        <div class="loading-spinner"></div>
                        <span>Загружаем автоматически...</span>
                    </div>` :
                    `<div class="animation-placeholder" onclick="lottieDemo.loadAnimation('${animation.id}', '${animation.file}')">
                        <span>👁️ Нажмите для загрузки</span>
                    </div>`;

                card.innerHTML = `
                    ${badge}
                    <div class="animation-container" data-animation="${animation.file}">
                        ${placeholder}
                    </div>
                    <div class="animation-info">
                        <div class="animation-title">${animation.title}</div>
                        <div class="animation-description">${animation.description}</div>
                        <div class="animation-stats">
                            <span>Размер: <span class="size-info">${animation.size}</span></span>
                            <span>Приоритет: ${animation.priority === 'high' ? '🔥 Высокий' : '⏳ Низкий'}</span>
                        </div>
                        <div class="animation-controls">
                            <button class="btn btn-primary" onclick="lottieDemo.playAnimation('${animation.id}')">▶️ Играть</button>
                            <button class="btn btn-secondary" onclick="lottieDemo.pauseAnimation('${animation.id}')">⏸️ Пауза</button>
                            <button class="btn btn-secondary" onclick="lottieDemo.restartAnimation('${animation.id}')">🔄 Сначала</button>
                        </div>
                    </div>
                `;
                card.id = animation.id;
                return card;
            }

            preloadAllAnimations() {
                // Разделяем анимации на приоритетные и обычные
                const priorityAnimations = this.animations.filter(anim => anim.preload === true);
                const lazyAnimations = this.animations.filter(anim => anim.preload === false);

                console.log(`🚀 Предзагружаем ${priorityAnimations.length} приоритетных анимаций`);
                console.log(`⏳ ${lazyAnimations.length} анимаций будут загружены по требованию`);

                // Загружаем только приоритетные анимации сразу
                priorityAnimations.forEach((animation, index) => {
                    setTimeout(() => {
                        this.loadAnimation(animation.id, animation.file);
                    }, index * 150); // Быстрее для приоритетных
                });

                // Настраиваем lazy loading для остальных
                this.setupLazyLoading(lazyAnimations);

                // Предзагружаем JSON файлы приоритетных анимаций
                this.preloadJsonFiles(priorityAnimations);
            }

            setupLazyLoading(lazyAnimations) {
                // Настраиваем Intersection Observer для lazy loading
                this.observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const animationId = entry.target.id;
                            const animation = lazyAnimations.find(anim => anim.id === animationId);

                            if (animation) {
                                const container = entry.target.querySelector('.animation-container');
                                if (!container.querySelector('lottie-player')) {
                                    console.log(`👁️ Загружаем по требованию: ${animation.title}`);
                                    this.loadAnimation(animation.id, animation.file);
                                    this.observer.unobserve(entry.target);
                                }
                            }
                        }
                    });
                }, {
                    rootMargin: '100px' // Загружаем за 100px до появления
                });

                // Наблюдаем только за lazy анимациями
                lazyAnimations.forEach(animation => {
                    const card = document.getElementById(animation.id);
                    if (card) {
                        this.observer.observe(card);
                    }
                });
            }

            async preloadJsonFiles(animations = this.animations) {
                console.log(`📦 Предзагружаем ${animations.length} JSON файлов в кеш...`);

                const preloadPromises = animations.map(async (animation) => {
                    try {
                        const response = await fetch(animation.file);
                        if (response.ok) {
                            const data = await response.json();
                            console.log(`✅ Предзагружен: ${animation.file}`);
                            return data;
                        }
                    } catch (error) {
                        console.warn(`⚠️ Не удалось предзагрузить: ${animation.file}`, error);
                    }
                });

                // Ждем загрузки всех файлов
                await Promise.allSettled(preloadPromises);
                console.log('🎯 Предзагрузка JSON файлов завершена');
            }

            async loadAnimation(animationId, animationFile) {
                const container = document.querySelector(`#${animationId} .animation-container`);
                const placeholder = container.querySelector('.animation-placeholder');
                
                // Показываем загрузку
                placeholder.className = 'animation-placeholder loading';
                placeholder.innerHTML = '<div class="loading-spinner"></div><span>Загружаем...</span>';

                try {
                    // Создаем lottie-player
                    const player = document.createElement('lottie-player');
                    player.setAttribute('src', animationFile);
                    player.setAttribute('background', 'transparent');
                    player.setAttribute('speed', '1');
                    player.setAttribute('loop', 'true');
                    player.setAttribute('autoplay', 'true');
                    
                    // Ждем загрузки
                    await new Promise((resolve, reject) => {
                        player.addEventListener('ready', resolve);
                        player.addEventListener('error', reject);
                        
                        // Таймаут на случай проблем
                        setTimeout(() => reject(new Error('Timeout')), 10000);
                    });

                    // Заменяем placeholder на player
                    container.innerHTML = '';
                    container.appendChild(player);
                    
                    // Плавное появление
                    setTimeout(() => {
                        player.classList.add('loaded');
                    }, 100);

                    console.log(`Анимация ${animationId} успешно загружена`);

                } catch (error) {
                    console.error(`Ошибка загрузки анимации ${animationId}:`, error);
                    placeholder.className = 'animation-placeholder';
                    placeholder.innerHTML = '❌ Ошибка загрузки<br><small>Проверьте путь к файлу</small>';
                }
            }

            playAnimation(animationId) {
                const player = document.querySelector(`#${animationId} lottie-player`);
                if (player) player.play();
            }

            pauseAnimation(animationId) {
                const player = document.querySelector(`#${animationId} lottie-player`);
                if (player) player.pause();
            }

            restartAnimation(animationId) {
                const player = document.querySelector(`#${animationId} lottie-player`);
                if (player) {
                    player.stop();
                    player.play();
                }
            }

            // Загрузить все lazy анимации принудительно
            loadAllLazy() {
                const lazyAnimations = this.animations.filter(anim => anim.preload === false);
                console.log(`🚀 Принудительно загружаем ${lazyAnimations.length} lazy анимаций`);

                lazyAnimations.forEach((animation, index) => {
                    const container = document.querySelector(`#${animation.id} .animation-container`);
                    if (!container.querySelector('lottie-player')) {
                        setTimeout(() => {
                            this.loadAnimation(animation.id, animation.file);
                        }, index * 300);
                    }
                });
            }

            // Показать статистику загрузки
            showLoadingStats() {
                const loaded = document.querySelectorAll('lottie-player').length;
                const total = this.animations.length;
                const preloadCount = this.animations.filter(anim => anim.preload).length;
                const lazyCount = this.animations.filter(anim => !anim.preload).length;

                alert(`📊 Статистика загрузки:

Всего анимаций: ${total}
Загружено: ${loaded}
Осталось: ${total - loaded}

Автозагрузка: ${preloadCount}
По требованию: ${lazyCount}`);
            }

            // Предзагрузить оставшиеся в фоне
            async preloadAllRemaining() {
                const lazyAnimations = this.animations.filter(anim => anim.preload === false);
                console.log(`📦 Предзагружаем ${lazyAnimations.length} оставшихся анимаций в фоне`);

                await this.preloadJsonFiles(lazyAnimations);
                alert('✅ Все анимации предзагружены в кеш браузера!');
            }
        }

        // Регистрация Service Worker для кеширования
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('sw.js').then(() => {
                console.log('🔧 Service Worker зарегистрирован для кеширования');
            }).catch(() => {
                console.log('⚠️ Service Worker не поддерживается');
            });
        }

        // Инициализация после загрузки DOM
        let lottieDemo;
        document.addEventListener('DOMContentLoaded', () => {
            lottieDemo = new LottieDemo();

            // Дополнительная оптимизация - предзагрузка через link prefetch
            lottieDemo.animations.forEach(animation => {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = animation.file;
                document.head.appendChild(link);
            });
        });
    </script>
</body>
</html>
