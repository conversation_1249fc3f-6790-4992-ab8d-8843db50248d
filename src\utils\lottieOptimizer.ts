interface LottieAsset {
  id: string;
  w: number;
  h: number;
  u?: string;
  p: string;
  e?: number;
}

interface LottieAnimation {
  v: string;
  fr: number;
  ip: number;
  op: number;
  w: number;
  h: number;
  nm: string;
  ddd: number;
  assets: LottieAsset[];
  layers: any[];
  markers?: any[];
}

export class LottieOptimizer {
  private qualityThreshold = 0.6; // Минимум 60% качества

  /**
   * Оптимизирует Lottie JSON файл
   */
  async optimizeLottie(lottieData: LottieAnimation): Promise<{
    optimized: LottieAnimation;
    originalSize: number;
    optimizedSize: number;
    compressionRatio: number;
  }> {
    const originalJson = JSON.stringify(lottieData);
    const originalSize = new Blob([originalJson]).size;

    // Создаем копию для оптимизации
    const optimized = JSON.parse(originalJson) as LottieAnimation;

    // Оптимизируем изображения в assets
    await this.optimizeAssets(optimized.assets);

    // Оптимизируем структуру JSON
    this.optimizeJsonStructure(optimized);

    // Округляем числовые значения
    this.roundNumbers(optimized);

    const optimizedJson = JSON.stringify(optimized);
    const optimizedSize = new Blob([optimizedJson]).size;
    const compressionRatio = (originalSize - optimizedSize) / originalSize;

    // Проверяем, что сжатие не менее 60%
    if (compressionRatio < this.qualityThreshold) {
      console.warn(`Compression ratio ${compressionRatio.toFixed(2)} is below threshold ${this.qualityThreshold}`);
    }

    return {
      optimized,
      originalSize,
      optimizedSize,
      compressionRatio
    };
  }

  /**
   * Оптимизирует изображения в assets
   */
  private async optimizeAssets(assets: LottieAsset[]): Promise<void> {
    for (const asset of assets) {
      if (asset.p && this.isBase64Image(asset.p)) {
        try {
          asset.p = await this.compressBase64Image(asset.p);
        } catch (error) {
          console.warn('Failed to compress image:', error);
        }
      }
    }
  }

  /**
   * Проверяет, является ли строка base64 изображением
   */
  private isBase64Image(str: string): boolean {
    return str.startsWith('data:image/');
  }

  /**
   * Сжимает base64 изображение
   */
  private async compressBase64Image(base64: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Cannot get canvas context'));
          return;
        }

        // Определяем новые размеры с сохранением пропорций
        const maxWidth = 1024;
        const maxHeight = 1024;
        let { width, height } = img;

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // Рисуем изображение с новыми размерами
        ctx.drawImage(img, 0, 0, width, height);

        // Конвертируем обратно в base64 с качеством 0.8
        const compressedBase64 = canvas.toDataURL('image/jpeg', 0.8);
        resolve(compressedBase64);
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = base64;
    });
  }

  /**
   * Оптимизирует структуру JSON
   */
  private optimizeJsonStructure(lottie: LottieAnimation): void {
    // Удаляем ненужные свойства
    this.removeUnusedProperties(lottie);
    
    // Оптимизируем слои
    this.optimizeLayers(lottie.layers);
  }

  /**
   * Удаляет неиспользуемые свойства
   */
  private removeUnusedProperties(obj: any): void {
    const unnecessaryProps = ['nm', 'mn', 'hd'];
    
    if (typeof obj === 'object' && obj !== null) {
      for (const prop of unnecessaryProps) {
        if (prop in obj) {
          delete obj[prop];
        }
      }

      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          this.removeUnusedProperties(obj[key]);
        }
      }
    }
  }

  /**
   * Оптимизирует слои
   */
  private optimizeLayers(layers: any[]): void {
    layers.forEach(layer => {
      // Удаляем скрытые слои
      if (layer.hd === true) {
        return;
      }

      // Оптимизируем keyframes
      if (layer.ks) {
        this.optimizeKeyframes(layer.ks);
      }

      // Рекурсивно обрабатываем вложенные слои
      if (layer.layers) {
        this.optimizeLayers(layer.layers);
      }
    });
  }

  /**
   * Оптимизирует keyframes
   */
  private optimizeKeyframes(ks: any): void {
    if (typeof ks === 'object' && ks !== null) {
      for (const key in ks) {
        if (ks[key] && typeof ks[key] === 'object') {
          if (ks[key].k && Array.isArray(ks[key].k)) {
            // Удаляем дублирующиеся keyframes
            ks[key].k = this.removeDuplicateKeyframes(ks[key].k);
          }
          this.optimizeKeyframes(ks[key]);
        }
      }
    }
  }

  /**
   * Удаляет дублирующиеся keyframes
   */
  private removeDuplicateKeyframes(keyframes: any[]): any[] {
    const unique = [];
    let lastKeyframe = null;

    for (const kf of keyframes) {
      if (!lastKeyframe || !this.areKeyframesEqual(kf, lastKeyframe)) {
        unique.push(kf);
        lastKeyframe = kf;
      }
    }

    return unique;
  }

  /**
   * Сравнивает два keyframe на равенство
   */
  private areKeyframesEqual(kf1: any, kf2: any): boolean {
    return JSON.stringify(kf1.s) === JSON.stringify(kf2.s) &&
           JSON.stringify(kf1.e) === JSON.stringify(kf2.e);
  }

  /**
   * Округляет числовые значения для уменьшения размера
   */
  private roundNumbers(obj: any, precision: number = 3): void {
    if (typeof obj === 'number') {
      return Math.round(obj * Math.pow(10, precision)) / Math.pow(10, precision);
    }

    if (Array.isArray(obj)) {
      for (let i = 0; i < obj.length; i++) {
        if (typeof obj[i] === 'number') {
          obj[i] = Math.round(obj[i] * Math.pow(10, precision)) / Math.pow(10, precision);
        } else {
          this.roundNumbers(obj[i], precision);
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (typeof obj[key] === 'number') {
            obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
          } else {
            this.roundNumbers(obj[key], precision);
          }
        }
      }
    }
  }
}

export const lottieOptimizer = new LottieOptimizer();
