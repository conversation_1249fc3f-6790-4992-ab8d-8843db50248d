interface OptimizationResult {
  optimized: any;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
}

interface ApiResponse {
  success: boolean;
  optimized: any;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  error?: string;
  details?: string;
}

const API_BASE_URL = '/api';

export async function optimizeLottieFile(file: File): Promise<OptimizationResult> {
  const formData = new FormData();
  formData.append('lottie', file);

  try {
    const response = await fetch(`${API_BASE_URL}/optimize`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Optimization failed');
    }

    return {
      optimized: data.optimized,
      originalSize: data.originalSize,
      optimizedSize: data.optimizedSize,
      compressionRatio: data.compressionRatio,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Неизвестная ошибка при оптимизации файла');
  }
}

export async function optimizeLottieJson(lottieData: any): Promise<OptimizationResult> {
  try {
    const response = await fetch(`${API_BASE_URL}/optimize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ lottieData }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse = await response.json();

    if (!data.success) {
      throw new Error(data.error || 'Optimization failed');
    }

    return {
      optimized: data.optimized,
      originalSize: data.originalSize,
      optimizedSize: data.optimizedSize,
      compressionRatio: data.compressionRatio,
    };
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Неизвестная ошибка при оптимизации JSON');
  }
}

export async function checkServerHealth(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    return response.ok;
  } catch {
    return false;
  }
}
