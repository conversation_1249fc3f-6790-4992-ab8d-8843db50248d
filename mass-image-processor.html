<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Массовый обработчик изображений</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-panel {
            background: white;
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            margin-bottom: 30px;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .setting-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
        }

        .setting-group label {
            display: block;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .setting-group input,
        .setting-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .setting-group input:focus,
        .setting-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .upload-zone {
            border: 4px dashed #667eea;
            border-radius: 20px;
            padding: 80px 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
            margin: 30px 0;
            position: relative;
        }

        .upload-zone:hover,
        .upload-zone.dragover {
            border-color: #764ba2;
            background: linear-gradient(135deg, #f0f2ff 0%, #e0e8ff 100%);
            transform: scale(1.02);
        }

        .upload-zone h2 {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-zone p {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .upload-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 25px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #495057;
            border: 2px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        .progress-panel {
            background: white;
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            margin-bottom: 30px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #e9ecef;
            border-radius: 15px;
            overflow: hidden;
            margin: 25px 0;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 15px;
        }

        .progress-text {
            text-align: center;
            font-size: 1.1rem;
            color: #495057;
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #dee2e6;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .file-input {
            display: none;
        }

        .info-panel {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 5px solid #2196f3;
        }

        .info-panel h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .info-panel ul {
            color: #424242;
            padding-left: 20px;
        }

        .info-panel li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .upload-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .upload-zone {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 Массовый обработчик изображений</h1>
            <p>Обработка целых папок с изображениями с сохранением структуры директорий</p>
        </header>

        <div class="info-panel">
            <h3>🎯 Возможности обработчика:</h3>
            <ul>
                <li><strong>Массовая обработка</strong> - загружайте целые папки с подпапками</li>
                <li><strong>Сохранение структуры</strong> - точное воспроизведение иерархии папок</li>
                <li><strong>Гибкие настройки</strong> - размер, качество, формат, режим обрезки</li>
                <li><strong>ZIP архив</strong> - все результаты в одном файле</li>
                <li><strong>Статистика</strong> - подробная информация о процессе</li>
            </ul>
        </div>

        <div class="main-panel">
            <h2 style="margin-bottom: 30px; color: #495057;">⚙️ Настройки обработки</h2>
            
            <div class="settings-grid">
                <div class="setting-group">
                    <label>Целевая ширина (px)</label>
                    <input type="number" id="targetWidth" value="1424" min="100" max="4000">
                </div>
                
                <div class="setting-group">
                    <label>Целевая высота (px)</label>
                    <input type="number" id="targetHeight" value="2532" min="100" max="4000">
                </div>
                
                <div class="setting-group">
                    <label>Режим изменения размера</label>
                    <select id="resizeMode">
                        <option value="fit">Вписать (с фоном)</option>
                        <option value="fill">Заполнить (с обрезкой)</option>
                        <option value="stretch">Растянуть (с искажением)</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label>Формат вывода</label>
                    <select id="outputFormat">
                        <option value="jpeg">JPEG (сжатие)</option>
                        <option value="png">PNG (без потерь)</option>
                        <option value="webp">WebP (современный)</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label>Качество сжатия</label>
                    <input type="range" id="compressionQuality" min="0.1" max="1" step="0.1" value="0.7">
                    <div style="text-align: center; margin-top: 5px; font-weight: 600;" id="qualityDisplay">70%</div>
                </div>
                
                <div class="setting-group">
                    <label>Цвет фона (для режима "Вписать")</label>
                    <input type="color" id="backgroundColor" value="#ffffff">
                </div>
            </div>

            <div class="upload-zone" id="uploadZone">
                <h2>📁 Перетащите папки или файлы сюда</h2>
                <p>Поддерживаются: PNG, JPG, JPEG, WebP</p>
                <p style="font-size: 0.9rem; opacity: 0.7;">Обрабатываются все подпапки рекурсивно</p>
                
                <div class="upload-buttons">
                    <button class="btn btn-secondary" onclick="selectFiles()">
                        📄 Выбрать файлы
                    </button>
                    <button class="btn btn-secondary" onclick="selectFolder()">
                        📁 Выбрать папку
                    </button>
                </div>
            </div>

            <input type="file" id="fileInput" class="file-input" accept="image/*" multiple>
            <input type="file" id="folderInput" class="file-input" webkitdirectory multiple>

            <div style="text-align: center; margin-top: 30px;">
                <button class="btn btn-primary" onclick="startProcessing()" id="processBtn" disabled>
                    🚀 Начать обработку
                </button>
                <button class="btn btn-success" onclick="downloadResults()" id="downloadBtn" disabled style="display: none;">
                    📦 Скачать ZIP архив
                </button>
                <button class="btn btn-secondary" onclick="clearAll()">
                    🗑️ Очистить всё
                </button>
            </div>
        </div>

        <div class="progress-panel" id="progressPanel">
            <h3 style="margin-bottom: 20px;">📊 Прогресс обработки</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Готов к запуску...</div>
            
            <div class="stats-grid" id="statsGrid">
                <div class="stat-card">
                    <div class="stat-number" id="totalFiles">0</div>
                    <div class="stat-label">Всего файлов</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="processedFiles">0</div>
                    <div class="stat-label">Обработано</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalFolders">0</div>
                    <div class="stat-label">Папок</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="compressionRatio">0%</div>
                    <div class="stat-label">Сжатие</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class MassImageProcessor {
            constructor() {
                this.selectedFiles = [];
                this.fileStructure = new Map();
                this.processedImages = [];
                this.totalOriginalSize = 0;
                this.totalNewSize = 0;
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                const uploadZone = document.getElementById('uploadZone');
                const fileInput = document.getElementById('fileInput');
                const folderInput = document.getElementById('folderInput');
                const qualitySlider = document.getElementById('compressionQuality');

                // Drag and drop
                uploadZone.addEventListener('dragover', this.handleDragOver.bind(this));
                uploadZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
                uploadZone.addEventListener('drop', this.handleDrop.bind(this));

                // File inputs
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                folderInput.addEventListener('change', this.handleFolderSelect.bind(this));

                // Quality slider
                qualitySlider.addEventListener('input', (e) => {
                    document.getElementById('qualityDisplay').textContent = Math.round(e.target.value * 100) + '%';
                });
            }

            handleDragOver(e) {
                e.preventDefault();
                document.getElementById('uploadZone').classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                document.getElementById('uploadZone').classList.remove('dragover');
            }

            async handleDrop(e) {
                e.preventDefault();
                document.getElementById('uploadZone').classList.remove('dragover');

                const items = Array.from(e.dataTransfer.items);
                const files = [];

                for (const item of items) {
                    if (item.kind === 'file') {
                        const entry = item.webkitGetAsEntry();
                        if (entry) {
                            await this.processEntry(entry, files, '');
                        }
                    }
                }

                this.processFileList(files);
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files).map(file => ({ file, path: file.name }));
                this.processFileList(files);
            }

            handleFolderSelect(e) {
                const files = Array.from(e.target.files).map(file => ({
                    file,
                    path: file.webkitRelativePath
                }));
                this.processFileList(files);
            }

            async processEntry(entry, files, path) {
                if (entry.isFile) {
                    const file = await this.getFileFromEntry(entry);
                    files.push({ file, path: path + entry.name });
                } else if (entry.isDirectory) {
                    const reader = entry.createReader();
                    const entries = await this.readEntries(reader);
                    for (const childEntry of entries) {
                        await this.processEntry(childEntry, files, path + entry.name + '/');
                    }
                }
            }

            getFileFromEntry(entry) {
                return new Promise((resolve, reject) => {
                    entry.file(resolve, reject);
                });
            }

            readEntries(reader) {
                return new Promise((resolve, reject) => {
                    reader.readEntries(resolve, reject);
                });
            }

            processFileList(files) {
                const imageFiles = files.filter(fileData =>
                    fileData.file.type.startsWith('image/') ||
                    /\.(png|jpg|jpeg|webp)$/i.test(fileData.file.name)
                );

                if (imageFiles.length === 0) {
                    alert('Не найдено изображений для обработки');
                    return;
                }

                this.selectedFiles = imageFiles.map(f => f.file);
                this.fileStructure.clear();
                imageFiles.forEach(f => this.fileStructure.set(f.file, f.path));

                this.updateUI();
            }

            updateUI() {
                const folders = new Set();
                this.fileStructure.forEach(path => {
                    const folderPath = path.substring(0, path.lastIndexOf('/'));
                    if (folderPath) folders.add(folderPath);
                });

                document.getElementById('totalFiles').textContent = this.selectedFiles.length;
                document.getElementById('totalFolders').textContent = folders.size;
                document.getElementById('processBtn').disabled = false;

                const uploadZone = document.getElementById('uploadZone');
                uploadZone.innerHTML = `
                    <h2>✅ Готово к обработке</h2>
                    <p><strong>${this.selectedFiles.length}</strong> файлов в <strong>${folders.size}</strong> папках</p>
                    <div class="upload-buttons">
                        <button class="btn btn-secondary" onclick="processor.selectNewFiles()">
                            🔄 Выбрать другие файлы
                        </button>
                    </div>
                `;
            }

            selectNewFiles() {
                this.selectedFiles = [];
                this.fileStructure.clear();
                this.processedImages = [];
                document.getElementById('processBtn').disabled = true;
                document.getElementById('downloadBtn').style.display = 'none';
                document.getElementById('progressPanel').style.display = 'none';

                const uploadZone = document.getElementById('uploadZone');
                uploadZone.innerHTML = `
                    <h2>📁 Перетащите папки или файлы сюда</h2>
                    <p>Поддерживаются: PNG, JPG, JPEG, WebP</p>
                    <p style="font-size: 0.9rem; opacity: 0.7;">Обрабатываются все подпапки рекурсивно</p>

                    <div class="upload-buttons">
                        <button class="btn btn-secondary" onclick="selectFiles()">
                            📄 Выбрать файлы
                        </button>
                        <button class="btn btn-secondary" onclick="selectFolder()">
                            📁 Выбрать папку
                        </button>
                    </div>
                `;
            }
        }

        const processor = new MassImageProcessor();

        function selectFiles() {
            document.getElementById('fileInput').click();
        }

        function selectFolder() {
            document.getElementById('folderInput').click();
        }

        async function startProcessing() {
            if (processor.selectedFiles.length === 0) {
                alert('Сначала выберите файлы для обработки');
                return;
            }

            document.getElementById('progressPanel').style.display = 'block';
            document.getElementById('processBtn').disabled = true;

            processor.processedImages = [];
            processor.totalOriginalSize = 0;
            processor.totalNewSize = 0;

            const settings = {
                targetWidth: parseInt(document.getElementById('targetWidth').value),
                targetHeight: parseInt(document.getElementById('targetHeight').value),
                resizeMode: document.getElementById('resizeMode').value,
                outputFormat: document.getElementById('outputFormat').value,
                compressionQuality: parseFloat(document.getElementById('compressionQuality').value),
                backgroundColor: document.getElementById('backgroundColor').value
            };

            for (let i = 0; i < processor.selectedFiles.length; i++) {
                const file = processor.selectedFiles[i];
                const path = processor.fileStructure.get(file);

                updateProgress(i, processor.selectedFiles.length, `Обрабатываем: ${path}`);

                try {
                    const processedImage = await processImage(file, settings);
                    processor.processedImages.push({
                        ...processedImage,
                        originalPath: path
                    });

                    processor.totalOriginalSize += file.size;
                    processor.totalNewSize += processedImage.blob.size;

                    document.getElementById('processedFiles').textContent = i + 1;

                    const compressionRatio = Math.round(
                        ((processor.totalOriginalSize - processor.totalNewSize) / processor.totalOriginalSize) * 100
                    );
                    document.getElementById('compressionRatio').textContent = compressionRatio + '%';

                } catch (error) {
                    console.error(`Ошибка обработки ${path}:`, error);
                }

                await new Promise(resolve => setTimeout(resolve, 50));
            }

            updateProgress(processor.selectedFiles.length, processor.selectedFiles.length, 'Обработка завершена!');
            document.getElementById('downloadBtn').style.display = 'inline-flex';
            document.getElementById('processBtn').disabled = false;
        }

        async function processImage(file, settings) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        canvas.width = settings.targetWidth;
                        canvas.height = settings.targetHeight;

                        let drawWidth, drawHeight, drawX, drawY;
                        let sourceX = 0, sourceY = 0, sourceWidth = img.width, sourceHeight = img.height;

                        switch (settings.resizeMode) {
                            case 'stretch':
                                drawWidth = settings.targetWidth;
                                drawHeight = settings.targetHeight;
                                drawX = 0;
                                drawY = 0;
                                break;

                            case 'fit':
                                const fitRatio = Math.min(settings.targetWidth / img.width, settings.targetHeight / img.height);
                                drawWidth = Math.round(img.width * fitRatio);
                                drawHeight = Math.round(img.height * fitRatio);
                                drawX = Math.round((settings.targetWidth - drawWidth) / 2);
                                drawY = Math.round((settings.targetHeight - drawHeight) / 2);

                                ctx.fillStyle = settings.backgroundColor;
                                ctx.fillRect(0, 0, settings.targetWidth, settings.targetHeight);
                                break;

                            case 'fill':
                                const fillRatio = Math.max(settings.targetWidth / img.width, settings.targetHeight / img.height);
                                const scaledWidth = img.width * fillRatio;
                                const scaledHeight = img.height * fillRatio;

                                if (scaledWidth > settings.targetWidth) {
                                    sourceWidth = settings.targetWidth / fillRatio;
                                    sourceX = (img.width - sourceWidth) / 2;
                                }

                                if (scaledHeight > settings.targetHeight) {
                                    sourceHeight = settings.targetHeight / fillRatio;
                                    sourceY = 0;
                                }

                                drawWidth = settings.targetWidth;
                                drawHeight = settings.targetHeight;
                                drawX = 0;
                                drawY = 0;
                                break;
                        }

                        ctx.drawImage(img, sourceX, sourceY, sourceWidth, sourceHeight, drawX, drawY, drawWidth, drawHeight);

                        let mimeType, fileExtension;
                        switch (settings.outputFormat) {
                            case 'jpeg':
                                mimeType = 'image/jpeg';
                                fileExtension = '.jpg';
                                break;
                            case 'webp':
                                mimeType = 'image/webp';
                                fileExtension = '.webp';
                                break;
                            default:
                                mimeType = 'image/png';
                                fileExtension = '.png';
                                break;
                        }

                        canvas.toBlob((blob) => {
                            if (blob) {
                                resolve({
                                    originalName: file.name,
                                    blob: blob,
                                    fileExtension: fileExtension
                                });
                            } else {
                                reject(new Error('Failed to create blob'));
                            }
                        }, mimeType, settings.outputFormat === 'png' ? undefined : settings.compressionQuality);

                    } catch (error) {
                        reject(error);
                    }
                };

                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = URL.createObjectURL(file);
            });
        }

        function updateProgress(current, total, message) {
            const progress = (current / total) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent = `${message} (${current}/${total})`;
        }

        async function downloadResults() {
            if (processor.processedImages.length === 0) {
                alert('Нет обработанных изображений для скачивания');
                return;
            }

            try {
                updateProgress(0, processor.processedImages.length, 'Создаем ZIP архив...');

                const zip = new JSZip();

                for (let i = 0; i < processor.processedImages.length; i++) {
                    const processedImage = processor.processedImages[i];
                    const pathParts = processedImage.originalPath.split('/');
                    const fileName = pathParts[pathParts.length - 1];
                    const folderPath = pathParts.slice(0, -1).join('/');

                    const baseName = fileName.replace(/\.[^/.]+$/, "");
                    const newFileName = `${baseName}_processed${processedImage.fileExtension}`;
                    const fullPath = folderPath ? `${folderPath}/${newFileName}` : newFileName;

                    zip.file(fullPath, processedImage.blob);

                    updateProgress(i + 1, processor.processedImages.length, `Добавляем: ${fullPath}`);
                    await new Promise(resolve => setTimeout(resolve, 10));
                }

                updateProgress(processor.processedImages.length, processor.processedImages.length, 'Генерируем архив...');

                const zipBlob = await zip.generateAsync({
                    type: "blob",
                    compression: "DEFLATE",
                    compressionOptions: { level: 6 }
                });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(zipBlob);

                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const settings = {
                    width: document.getElementById('targetWidth').value,
                    height: document.getElementById('targetHeight').value
                };
                link.download = `processed_images_${settings.width}x${settings.height}_${timestamp}.zip`;

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(link.href);

                updateProgress(processor.processedImages.length, processor.processedImages.length, 'Архив готов!');

            } catch (error) {
                console.error('Ошибка создания архива:', error);
                alert('Ошибка при создании ZIP архива: ' + error.message);
            }
        }

        function clearAll() {
            processor.selectNewFiles();
            document.getElementById('totalFiles').textContent = '0';
            document.getElementById('processedFiles').textContent = '0';
            document.getElementById('totalFolders').textContent = '0';
            document.getElementById('compressionRatio').textContent = '0%';
        }
    </script>
</body>
</html>
