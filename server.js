import express from 'express';
import multer from 'multer';
import cors from 'cors';
import sharp from 'sharp';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const port = 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Multer configuration for file uploads
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// Lottie Optimizer class (server-side version)
class ServerLottieOptimizer {
  constructor() {
    this.qualityThreshold = 0.6;
  }

  async optimizeLottie(lottieData) {
    const originalJson = JSON.stringify(lottieData);
    const originalSize = Buffer.byteLength(originalJson, 'utf8');

    const optimized = JSON.parse(originalJson);

    // Optimize assets with server-side image processing
    await this.optimizeAssets(optimized.assets);
    
    // Optimize JSON structure
    this.optimizeJsonStructure(optimized);
    
    // Round numbers
    this.roundNumbers(optimized);

    const optimizedJson = JSON.stringify(optimized);
    const optimizedSize = Buffer.byteLength(optimizedJson, 'utf8');
    const compressionRatio = (originalSize - optimizedSize) / originalSize;

    return {
      optimized,
      originalSize,
      optimizedSize,
      compressionRatio
    };
  }

  async optimizeAssets(assets) {
    if (!assets || !Array.isArray(assets)) return;

    for (const asset of assets) {
      if (asset.p && this.isBase64Image(asset.p)) {
        try {
          asset.p = await this.compressBase64ImageServer(asset.p);
        } catch (error) {
          console.warn('Failed to compress image:', error);
        }
      }
    }
  }

  isBase64Image(str) {
    return typeof str === 'string' && str.startsWith('data:image/');
  }

  async compressBase64ImageServer(base64) {
    try {
      // Extract base64 data
      const base64Data = base64.split(',')[1];
      const buffer = Buffer.from(base64Data, 'base64');

      // Compress with Sharp
      const compressedBuffer = await sharp(buffer)
        .resize(1024, 1024, { 
          fit: 'inside',
          withoutEnlargement: true 
        })
        .jpeg({ 
          quality: 80,
          progressive: true 
        })
        .toBuffer();

      // Convert back to base64
      const compressedBase64 = `data:image/jpeg;base64,${compressedBuffer.toString('base64')}`;
      return compressedBase64;
    } catch (error) {
      console.error('Image compression error:', error);
      return base64; // Return original if compression fails
    }
  }

  optimizeJsonStructure(lottie) {
    this.removeUnusedProperties(lottie);
    if (lottie.layers) {
      this.optimizeLayers(lottie.layers);
    }
  }

  removeUnusedProperties(obj) {
    const unnecessaryProps = ['nm', 'mn', 'hd'];
    
    if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
      for (const prop of unnecessaryProps) {
        if (prop in obj) {
          delete obj[prop];
        }
      }

      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          this.removeUnusedProperties(obj[key]);
        }
      }
    } else if (Array.isArray(obj)) {
      obj.forEach(item => this.removeUnusedProperties(item));
    }
  }

  optimizeLayers(layers) {
    if (!Array.isArray(layers)) return;

    layers.forEach(layer => {
      if (layer.hd === true) return;

      if (layer.ks) {
        this.optimizeKeyframes(layer.ks);
      }

      if (layer.layers) {
        this.optimizeLayers(layer.layers);
      }
    });
  }

  optimizeKeyframes(ks) {
    if (typeof ks === 'object' && ks !== null) {
      for (const key in ks) {
        if (ks[key] && typeof ks[key] === 'object') {
          if (ks[key].k && Array.isArray(ks[key].k)) {
            ks[key].k = this.removeDuplicateKeyframes(ks[key].k);
          }
          this.optimizeKeyframes(ks[key]);
        }
      }
    }
  }

  removeDuplicateKeyframes(keyframes) {
    const unique = [];
    let lastKeyframe = null;

    for (const kf of keyframes) {
      if (!lastKeyframe || !this.areKeyframesEqual(kf, lastKeyframe)) {
        unique.push(kf);
        lastKeyframe = kf;
      }
    }

    return unique;
  }

  areKeyframesEqual(kf1, kf2) {
    return JSON.stringify(kf1.s) === JSON.stringify(kf2.s) &&
           JSON.stringify(kf1.e) === JSON.stringify(kf2.e);
  }

  roundNumbers(obj, precision = 3) {
    if (typeof obj === 'number') {
      return Math.round(obj * Math.pow(10, precision)) / Math.pow(10, precision);
    }

    if (Array.isArray(obj)) {
      for (let i = 0; i < obj.length; i++) {
        if (typeof obj[i] === 'number') {
          obj[i] = Math.round(obj[i] * Math.pow(10, precision)) / Math.pow(10, precision);
        } else {
          this.roundNumbers(obj[i], precision);
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (typeof obj[key] === 'number') {
            obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
          } else {
            this.roundNumbers(obj[key], precision);
          }
        }
      }
    }
  }
}

const optimizer = new ServerLottieOptimizer();

// Routes
app.post('/api/optimize', upload.single('lottie'), async (req, res) => {
  try {
    let lottieData;

    if (req.file) {
      // File upload
      const fileContent = req.file.buffer.toString('utf8');
      lottieData = JSON.parse(fileContent);
    } else if (req.body.lottieData) {
      // JSON data
      lottieData = req.body.lottieData;
    } else {
      return res.status(400).json({ error: 'No Lottie data provided' });
    }

    const result = await optimizer.optimizeLottie(lottieData);
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Optimization error:', error);
    res.status(500).json({ 
      error: 'Failed to optimize Lottie file',
      details: error.message 
    });
  }
});

app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(port, () => {
  console.log(`Lottie Optimizer server running on port ${port}`);
});
