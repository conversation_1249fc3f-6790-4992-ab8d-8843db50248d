# Lottie Optimizer

Мощный инструмент для оптимизации Lottie JSON файлов с сжатием изображений и оптимизацией структуры данных.

## Особенности

- 🎯 **Сжатие изображений**: Автоматическое сжатие base64 изображений в Lottie файлах
- 📊 **Оптимизация JSON**: Удаление неиспользуемых свойств и оптимизация структуры
- 🔢 **Округление чисел**: Уменьшение точности числовых значений для экономии места
- 📈 **Гарантия качества**: Сохранение качества не менее 60%
- 🖱️ **Drag & Drop**: Удобный интерфейс с поддержкой перетаскивания файлов
- 📱 **Адаптивный дизайн**: Работает на всех устройствах

## Как использовать

### Основной интерфейс

1. Откройте `index.html` в браузере
2. Перетащите Lottie JSON файл в область загрузки или нажмите для выбора файла
3. Дождитесь завершения оптимизации
4. Просмотрите результаты и скачайте оптимизированный файл

### Просмотр результатов оптимизации

1. Откройте `results.html` в браузере для автоматической обработки всех файлов
2. Просмотрите детальную статистику по каждому файлу
3. Скачайте отдельные оптимизированные файлы или все сразу
4. Изучите демонстрацию оптимизированных анимаций

### Страница результатов

1. Откройте `results.html` в браузере
2. Автоматически запустится обработка всех файлов из папки `mob/output`
3. Просмотрите детальную статистику оптимизации в стиле https://admins.today/json
4. Скачайте отдельные оптимизированные файлы или все сразу

### Пакетное тестирование

1. Откройте `test-optimizer.html` в браузере
2. Нажмите "Запустить пакетное тестирование"
3. Просмотрите результаты для всех файлов в папке `mob/output`
4. Скачайте отдельные оптимизированные файлы или просмотрите общую статистику

## Структура проекта

```
optimizm/
├── index.html              # Основной интерфейс оптимизатора
├── test-optimizer.html     # Инструмент пакетного тестирования
├── README.md              # Документация
├── mob/output/            # Готовые Lottie файлы для тестирования
│   ├── 1.json
│   ├── 2.json
│   └── ...
└── full/                  # Исходные файлы (если доступны)
```

## Алгоритм оптимизации

### 1. Сжатие изображений
- Обнаружение base64 изображений в assets
- Изменение размера до максимум 1024x1024 пикселей
- Сжатие в JPEG с качеством 80%
- Сохранение пропорций изображения

### 2. Оптимизация JSON структуры
- Удаление неиспользуемых свойств (`nm`, `mn`, `hd`)
- Оптимизация keyframes (удаление дубликатов)
- Очистка скрытых слоев
- Рекурсивная обработка вложенных структур

### 3. Округление чисел
- Округление числовых значений до 3 знаков после запятой
- Уменьшение размера JSON без потери визуального качества

## Технические характеристики

- **Минимальное сжатие**: 60% (настраивается)
- **Максимальный размер изображения**: 1024x1024 пикселей
- **Качество JPEG**: 80%
- **Точность чисел**: 3 знака после запятой
- **Поддерживаемые форматы**: JSON файлы с Lottie анимациями

## Результаты оптимизации

Оптимизатор показывает:
- Исходный размер файла
- Размер после оптимизации
- Процент сжатия
- Количество сэкономленных байт

## Совместимость

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 11+
- ✅ Edge 79+

## Безопасность

- Все операции выполняются локально в браузере
- Файлы не отправляются на сервер
- Полная конфиденциальность данных

## Примеры использования

### Веб-разработка
Оптимизируйте Lottie анимации для веб-сайтов, чтобы уменьшить время загрузки страниц.

### Мобильные приложения
Сократите размер APK/IPA файлов за счет оптимизации встроенных анимаций.

### Электронная почта
Уменьшите размер HTML писем с анимациями для лучшей доставляемости.

## Ограничения

- Работает только с Lottie файлами в формате JSON
- Требует современный браузер с поддержкой Canvas API
- Большие файлы могут требовать больше времени на обработку

## Поддержка

Если у вас возникли проблемы или вопросы:
1. Проверьте, что файл является валидным Lottie JSON
2. Убедитесь, что браузер поддерживает все необходимые API
3. Попробуйте с файлом меньшего размера

## Лицензия

Этот проект создан для демонстрации возможностей оптимизации Lottie файлов.
