<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Массовый оптимизатор Lottie - 712x1266</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .control-panel {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-group label {
            font-weight: 600;
            color: #333;
        }

        .control-group input,
        .control-group select {
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1rem;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }

        .progress-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .result-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-5px);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .file-name {
            font-weight: 600;
            color: #333;
        }

        .compression-badge {
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }

        .compression-excellent { background: #28a745; }
        .compression-good { background: #17a2b8; }
        .compression-fair { background: #ffc107; color: #333; }

        .stats-mini {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .lottie-preview {
            width: 100%;
            height: 150px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 15px;
            overflow: hidden;
            position: relative;
        }

        .lottie-animation {
            width: 100%;
            height: 100%;
        }

        .summary-stats {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: none;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .summary-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }

        .summary-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .error {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .success {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            color: #2f855a;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        @media (max-width: 768px) {
            .controls {
                grid-template-columns: 1fr;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🎬 Массовый оптимизатор Lottie</h1>
            <p>Оптимизация всех файлов с изменением размера изображений до 712x1266</p>
        </header>

        <div class="control-panel">
            <h2>Настройки оптимизации</h2>
            <div class="controls">
                <div class="control-group">
                    <label>Целевая ширина:</label>
                    <input type="number" id="targetWidth" value="712" min="100" max="4000">
                </div>
                <div class="control-group">
                    <label>Целевая высота:</label>
                    <input type="number" id="targetHeight" value="1266" min="100" max="4000">
                </div>
                <div class="control-group">
                    <label>Качество JPEG (0.1-1.0):</label>
                    <input type="number" id="jpegQuality" value="0.8" min="0.1" max="1.0" step="0.1">
                </div>
                <div class="control-group">
                    <label>Режим изменения размера:</label>
                    <select id="resizeMode">
                        <option value="stretch">Растянуть до размера</option>
                        <option value="fit">Вписать с сохранением пропорций</option>
                        <option value="fill">Заполнить с обрезкой</option>
                    </select>
                </div>
            </div>

            <div class="upload-section" style="margin: 30px 0; padding: 30px; border: 3px dashed #667eea; border-radius: 15px; text-align: center; background: #f8f9ff;">
                <h3 style="color: #667eea; margin-bottom: 15px;">📁 Загрузка файлов</h3>
                <p style="margin-bottom: 20px; color: #666;">Перетащите JSON файлы или папки сюда, или выберите через кнопки</p>

                <div style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                    <button class="btn btn-secondary" onclick="selectFiles()">
                        📄 Выбрать файлы
                    </button>
                    <button class="btn btn-secondary" onclick="selectFolder()">
                        📁 Выбрать папку
                    </button>
                    <button class="btn btn-secondary" onclick="loadDefaultFiles()">
                        🎬 Загрузить из mob/output
                    </button>
                </div>

                <div id="fileStatus" style="margin-top: 15px; font-weight: 600; color: #333;"></div>
            </div>

            <input type="file" id="fileInput" accept=".json" multiple style="display: none;">
            <input type="file" id="folderInput" webkitdirectory multiple style="display: none;">

            <div style="text-align: center;">
                <button class="btn btn-primary" onclick="startBatchOptimization()" id="optimizeBtn" disabled>
                    🚀 Запустить массовую оптимизацию
                </button>
                <button class="btn btn-secondary" onclick="downloadAllOptimized()">
                    📥 Скачать все оптимизированные
                </button>
                <button class="btn btn-secondary" onclick="clearResults()">
                    🗑️ Очистить результаты
                </button>
            </div>
        </div>

        <div class="progress-section" id="progressSection">
            <h3>Прогресс обработки</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Готов к запуску...</div>
        </div>

        <div class="summary-stats" id="summaryStats">
            <h2>Общая статистика</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3 id="totalFiles">0</h3>
                    <p>Обработано файлов</p>
                </div>
                <div class="summary-card">
                    <h3 id="totalOriginalSize">0 MB</h3>
                    <p>Исходный размер</p>
                </div>
                <div class="summary-card">
                    <h3 id="totalOptimizedSize">0 MB</h3>
                    <p>Оптимизированный размер</p>
                </div>
                <div class="summary-card">
                    <h3 id="averageCompression">0%</h3>
                    <p>Среднее сжатие</p>
                </div>
            </div>
        </div>

        <div class="results-grid" id="resultsGrid"></div>
    </div>

    <script>
        class BatchLottieOptimizer {
            constructor() {
                this.optimizationResults = [];
                this.isProcessing = false;
                this.lottieAnimations = {};
                this.selectedFiles = [];
                this.fileStructure = new Map();
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                const uploadSection = document.querySelector('.upload-section');
                const fileInput = document.getElementById('fileInput');
                const folderInput = document.getElementById('folderInput');

                // Drag and drop
                uploadSection.addEventListener('dragover', this.handleDragOver.bind(this));
                uploadSection.addEventListener('dragleave', this.handleDragLeave.bind(this));
                uploadSection.addEventListener('drop', this.handleDrop.bind(this));

                // File inputs
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                folderInput.addEventListener('change', this.handleFolderSelect.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                e.currentTarget.style.borderColor = '#764ba2';
                e.currentTarget.style.background = '#f0f2ff';
            }

            handleDragLeave(e) {
                e.preventDefault();
                e.currentTarget.style.borderColor = '#667eea';
                e.currentTarget.style.background = '#f8f9ff';
            }

            async handleDrop(e) {
                e.preventDefault();
                e.currentTarget.style.borderColor = '#667eea';
                e.currentTarget.style.background = '#f8f9ff';

                const items = Array.from(e.dataTransfer.items);
                const files = [];

                for (const item of items) {
                    if (item.kind === 'file') {
                        const entry = item.webkitGetAsEntry();
                        if (entry) {
                            await this.processEntry(entry, files, '');
                        }
                    }
                }

                this.processFileList(files);
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files).map(file => ({ file, path: file.name }));
                this.processFileList(files);
            }

            handleFolderSelect(e) {
                const files = Array.from(e.target.files).map(file => ({
                    file,
                    path: file.webkitRelativePath
                }));
                this.processFileList(files);
            }

            async processEntry(entry, files, path) {
                if (entry.isFile) {
                    try {
                        const file = await this.getFileFromEntry(entry);
                        files.push({ file, path: path + entry.name });
                    } catch (error) {
                        console.warn(`Не удалось прочитать файл: ${path + entry.name}`, error);
                    }
                } else if (entry.isDirectory) {
                    try {
                        const reader = entry.createReader();
                        const allEntries = await this.readAllEntries(reader);

                        for (const childEntry of allEntries) {
                            await this.processEntry(childEntry, files, path + entry.name + '/');
                        }
                    } catch (error) {
                        console.warn(`Не удалось прочитать папку: ${path + entry.name}`, error);
                    }
                }
            }

            getFileFromEntry(entry) {
                return new Promise((resolve, reject) => {
                    entry.file(resolve, reject);
                });
            }

            async readAllEntries(reader) {
                const allEntries = [];
                let entries;

                do {
                    entries = await this.readEntries(reader);
                    allEntries.push(...entries);
                } while (entries.length > 0);

                return allEntries;
            }

            readEntries(reader) {
                return new Promise((resolve, reject) => {
                    reader.readEntries(
                        (entries) => resolve(entries),
                        (error) => {
                            console.warn('Ошибка чтения папки:', error);
                            resolve([]);
                        }
                    );
                });
            }

            processFileList(files) {
                const jsonFiles = files.filter(fileData =>
                    fileData.file.type === 'application/json' ||
                    fileData.file.name.toLowerCase().endsWith('.json')
                );

                if (jsonFiles.length === 0) {
                    alert('Не найдено JSON файлов для обработки');
                    return;
                }

                this.selectedFiles = jsonFiles.map(f => f.file);
                this.fileStructure.clear();
                jsonFiles.forEach(f => this.fileStructure.set(f.file, f.path));

                this.updateFileStatus();
            }

            updateFileStatus() {
                const folders = new Set();
                this.fileStructure.forEach(path => {
                    const folderPath = path.substring(0, path.lastIndexOf('/'));
                    if (folderPath) folders.add(folderPath);
                });

                const fileStatus = document.getElementById('fileStatus');
                fileStatus.innerHTML = `✅ Загружено: <strong>${this.selectedFiles.length}</strong> JSON файлов из <strong>${folders.size}</strong> папок`;

                document.getElementById('optimizeBtn').disabled = false;
            }

            async startBatchOptimization() {
                if (this.isProcessing) return;

                // Проверяем есть ли загруженные файлы
                if (this.selectedFiles.length === 0) {
                    alert('Сначала загрузите JSON файлы');
                    return;
                }

                this.isProcessing = true;
                this.optimizationResults = [];

                const progressSection = document.getElementById('progressSection');
                const resultsGrid = document.getElementById('resultsGrid');
                const summaryStats = document.getElementById('summaryStats');

                progressSection.style.display = 'block';
                resultsGrid.innerHTML = '';
                summaryStats.style.display = 'none';

                let processedCount = 0;
                let totalOriginalSize = 0;
                let totalOptimizedSize = 0;

                for (const file of this.selectedFiles) {
                    try {
                        const filePath = this.fileStructure.get(file);
                        this.updateProgress(processedCount, this.selectedFiles.length, `Обрабатываем ${filePath}...`);

                        const fileContent = await this.readFileAsText(file);
                        const lottieData = JSON.parse(fileContent);
                        const result = await this.optimizeLottie(lottieData);

                        const optimizationResult = {
                            fileName: file.name,
                            filePath: filePath,
                            originalSize: result.originalSize,
                            optimizedSize: result.optimizedSize,
                            compressionRatio: result.compressionRatio,
                            optimizedData: result.optimized,
                            originalData: result.original
                        };

                        this.optimizationResults.push(optimizationResult);
                        totalOriginalSize += result.originalSize;
                        totalOptimizedSize += result.optimizedSize;

                        this.displayResult(optimizationResult);
                        processedCount++;

                    } catch (error) {
                        console.error(`Ошибка при обработке ${file.name}:`, error);
                        this.displayError(file.name, error.message);
                        processedCount++;
                    }

                    // Небольшая задержка для предотвращения блокировки UI
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                this.updateProgress(this.selectedFiles.length, this.selectedFiles.length, 'Обработка завершена!');
                this.updateSummaryStats(totalOriginalSize, totalOptimizedSize, processedCount);
                this.isProcessing = false;
            }

            readFileAsText(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => resolve(e.target.result);
                    reader.onerror = reject;
                    reader.readAsText(file);
                });
            }

            async optimizeLottie(lottieData) {
                const originalJson = JSON.stringify(lottieData);
                const originalSize = new Blob([originalJson]).size;

                // Создаем копию для оптимизации
                const optimized = JSON.parse(originalJson);

                // Получаем настройки из UI
                const targetWidth = parseInt(document.getElementById('targetWidth').value);
                const targetHeight = parseInt(document.getElementById('targetHeight').value);
                const jpegQuality = parseFloat(document.getElementById('jpegQuality').value);
                const resizeMode = document.getElementById('resizeMode').value;

                console.log(`Применяем размеры: ${targetWidth}x${targetHeight}, качество: ${jpegQuality}, режим: ${resizeMode}`);

                // Оптимизируем assets с новыми параметрами
                await this.optimizeAssets(optimized.assets || [], targetWidth, targetHeight, jpegQuality, resizeMode);

                // Обновляем размеры композиции
                console.log(`Изменяем размер композиции с ${optimized.w}x${optimized.h} на ${targetWidth}x${targetHeight}`);
                optimized.w = targetWidth;
                optimized.h = targetHeight;

                // БЕЗОПАСНАЯ оптимизация - только округление чисел
                // НЕ удаляем свойства, чтобы не сломать анимацию
                this.safeRoundNumbers(optimized);

                const optimizedJson = JSON.stringify(optimized);
                const optimizedSize = new Blob([optimizedJson]).size;
                const compressionRatio = (originalSize - optimizedSize) / originalSize;

                return {
                    original: lottieData,
                    optimized,
                    originalSize,
                    optimizedSize,
                    compressionRatio
                };
            }

            async optimizeAssets(assets, targetWidth, targetHeight, quality, resizeMode) {
                if (!assets || !Array.isArray(assets)) return;

                for (const asset of assets) {
                    if (asset.p && this.isBase64Image(asset.p)) {
                        try {
                            console.log(`Обрабатываем изображение размером ${asset.w || 'unknown'}x${asset.h || 'unknown'}`);

                            const newBase64 = await this.resizeAndCompressImage(asset.p, targetWidth, targetHeight, quality, resizeMode);

                            // Проверяем что новый base64 валидный
                            if (newBase64 && newBase64.startsWith('data:image/')) {
                                asset.p = newBase64;
                                asset.w = targetWidth;
                                asset.h = targetHeight;
                                console.log(`Изображение успешно изменено на ${targetWidth}x${targetHeight}`);
                            } else {
                                console.warn('Получен невалидный base64, оставляем оригинал');
                            }
                        } catch (error) {
                            console.warn('Ошибка при обработке изображения, оставляем оригинал:', error);
                            // НЕ изменяем asset.p при ошибке
                        }
                    }
                }
            }

            isBase64Image(str) {
                return str.startsWith('data:image/');
            }

            async resizeAndCompressImage(base64, targetWidth, targetHeight, quality, resizeMode) {
                return new Promise((resolve, reject) => {
                    // Проверяем валидность входного base64
                    if (!base64 || !base64.startsWith('data:image/')) {
                        reject(new Error('Invalid base64 image'));
                        return;
                    }

                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        
                        if (!ctx) {
                            reject(new Error('Cannot get canvas context'));
                            return;
                        }

                        canvas.width = targetWidth;
                        canvas.height = targetHeight;

                        console.log(`Изменяем размер изображения на ${targetWidth}x${targetHeight}`);

                        let drawWidth, drawHeight, drawX, drawY;

                        switch (resizeMode) {
                            case 'stretch':
                                // Растягиваем до целевого размера
                                drawWidth = targetWidth;
                                drawHeight = targetHeight;
                                drawX = 0;
                                drawY = 0;
                                break;
                                
                            case 'fit':
                                // Вписываем с сохранением пропорций
                                const fitRatio = Math.min(targetWidth / img.width, targetHeight / img.height);
                                drawWidth = img.width * fitRatio;
                                drawHeight = img.height * fitRatio;
                                drawX = (targetWidth - drawWidth) / 2;
                                drawY = (targetHeight - drawHeight) / 2;
                                
                                // Заливаем фон белым
                                ctx.fillStyle = 'white';
                                ctx.fillRect(0, 0, targetWidth, targetHeight);
                                break;
                                
                            case 'fill':
                                // Заполняем с обрезкой
                                const fillRatio = Math.max(targetWidth / img.width, targetHeight / img.height);
                                drawWidth = img.width * fillRatio;
                                drawHeight = img.height * fillRatio;
                                drawX = (targetWidth - drawWidth) / 2;
                                drawY = (targetHeight - drawHeight) / 2;
                                break;
                        }

                        ctx.drawImage(img, drawX, drawY, drawWidth, drawHeight);

                        // Используем высокое качество для JPEG (минимум 0.8)
                        const finalQuality = Math.max(quality, 0.8);
                        const compressedBase64 = canvas.toDataURL('image/jpeg', finalQuality);

                        // Проверяем что результат валидный
                        if (compressedBase64 && compressedBase64.startsWith('data:image/')) {
                            resolve(compressedBase64);
                        } else {
                            reject(new Error('Failed to generate valid base64'));
                        }
                    };

                    img.onerror = () => reject(new Error('Failed to load image'));
                    img.src = base64;
                });
            }

            // ОТКЛЮЧЕНО: optimizeJsonStructure - ломает файлы
            optimizeJsonStructure(lottie) {
                // НЕ УДАЛЯЕМ СВОЙСТВА - это ломает анимации
                // this.removeUnusedProperties(lottie);
                // if (lottie.layers) {
                //     this.optimizeLayers(lottie.layers);
                // }
                console.log('Структурная оптимизация отключена для безопасности');
            }

            removeUnusedProperties(obj) {
                const unnecessaryProps = ['nm', 'mn', 'hd'];
                
                if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
                    for (const prop of unnecessaryProps) {
                        if (prop in obj) {
                            delete obj[prop];
                        }
                    }

                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            this.removeUnusedProperties(obj[key]);
                        }
                    }
                } else if (Array.isArray(obj)) {
                    obj.forEach(item => this.removeUnusedProperties(item));
                }
            }

            optimizeLayers(layers) {
                if (!Array.isArray(layers)) return;

                layers.forEach(layer => {
                    if (layer.hd === true) return;

                    if (layer.ks) {
                        this.optimizeKeyframes(layer.ks);
                    }

                    if (layer.layers) {
                        this.optimizeLayers(layer.layers);
                    }
                });
            }

            optimizeKeyframes(ks) {
                if (typeof ks === 'object' && ks !== null) {
                    for (const key in ks) {
                        if (ks[key] && typeof ks[key] === 'object') {
                            if (ks[key].k && Array.isArray(ks[key].k)) {
                                ks[key].k = this.removeDuplicateKeyframes(ks[key].k);
                            }
                            this.optimizeKeyframes(ks[key]);
                        }
                    }
                }
            }

            removeDuplicateKeyframes(keyframes) {
                const unique = [];
                let lastKeyframe = null;

                for (const kf of keyframes) {
                    if (!lastKeyframe || !this.areKeyframesEqual(kf, lastKeyframe)) {
                        unique.push(kf);
                        lastKeyframe = kf;
                    }
                }

                return unique;
            }

            areKeyframesEqual(kf1, kf2) {
                return JSON.stringify(kf1.s) === JSON.stringify(kf2.s) &&
                       JSON.stringify(kf1.e) === JSON.stringify(kf2.e);
            }

            // БЕЗОПАСНАЯ функция округления - не трогает критические свойства
            safeRoundNumbers(obj, precision = 2) {
                // Список критических свойства, которые НЕ округляем
                const criticalProps = ['v', 'fr', 'ip', 'op', 'w', 'h', 'ind', 'ty', 'refId'];

                if (typeof obj === 'number') {
                    return Math.round(obj * Math.pow(10, precision)) / Math.pow(10, precision);
                }

                if (Array.isArray(obj)) {
                    for (let i = 0; i < obj.length; i++) {
                        if (typeof obj[i] === 'number') {
                            obj[i] = Math.round(obj[i] * Math.pow(10, precision)) / Math.pow(10, precision);
                        } else if (typeof obj[i] === 'object') {
                            this.safeRoundNumbers(obj[i], precision);
                        }
                    }
                } else if (typeof obj === 'object' && obj !== null) {
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            // Пропускаем критические свойства
                            if (criticalProps.includes(key)) {
                                continue;
                            }

                            if (typeof obj[key] === 'number') {
                                obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
                            } else if (typeof obj[key] === 'object') {
                                this.safeRoundNumbers(obj[key], precision);
                            }
                        }
                    }
                }
            }

            // Старая функция округления (не используется)
            roundNumbers(obj, precision = 3) {
                if (typeof obj === 'number') {
                    return Math.round(obj * Math.pow(10, precision)) / Math.pow(10, precision);
                }

                if (Array.isArray(obj)) {
                    for (let i = 0; i < obj.length; i++) {
                        if (typeof obj[i] === 'number') {
                            obj[i] = Math.round(obj[i] * Math.pow(10, precision)) / Math.pow(10, precision);
                        } else {
                            this.roundNumbers(obj[i], precision);
                        }
                    }
                } else if (typeof obj === 'object' && obj !== null) {
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            if (typeof obj[key] === 'number') {
                                obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
                            } else {
                                this.roundNumbers(obj[key], precision);
                            }
                        }
                    }
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            updateProgress(current, total, message) {
                const progress = (current / total) * 100;
                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('progressText').textContent = `${message} (${current}/${total})`;
            }

            displayResult(result) {
                const resultsGrid = document.getElementById('resultsGrid');
                const compressionPercent = Math.round(result.compressionRatio * 100);
                
                let compressionClass = 'compression-fair';
                if (compressionPercent >= 90) compressionClass = 'compression-excellent';
                else if (compressionPercent >= 70) compressionClass = 'compression-good';

                const resultCard = document.createElement('div');
                resultCard.className = 'result-card';
                resultCard.innerHTML = `
                    <div class="result-header">
                        <span class="file-name">${result.fileName}</span>
                        <span class="compression-badge ${compressionClass}">${compressionPercent}%</span>
                    </div>
                    <div class="stats-mini">
                        <div>Исходный: ${this.formatFileSize(result.originalSize)}</div>
                        <div>Оптимизированный: ${this.formatFileSize(result.optimizedSize)}</div>
                        <div>Сэкономлено: ${this.formatFileSize(result.originalSize - result.optimizedSize)}</div>
                        <div>Размер: 1080x1920</div>
                    </div>
                    <div class="lottie-preview" id="preview-${result.fileName}">
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">
                            🎬 Загружаем анимацию...
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="downloadSingle('${result.fileName}')">
                        📥 Скачать
                    </button>
                `;
                
                resultsGrid.appendChild(resultCard);
                
                // Загружаем анимацию
                this.loadPreviewAnimation(result.fileName, result.optimizedData);
            }

            displayError(fileName, errorMessage) {
                const resultsGrid = document.getElementById('resultsGrid');
                const resultCard = document.createElement('div');
                resultCard.className = 'result-card';
                resultCard.innerHTML = `
                    <div class="result-header">
                        <span class="file-name">${fileName}</span>
                        <span style="color: #dc3545;">❌ Ошибка</span>
                    </div>
                    <div class="error">
                        ${errorMessage}
                    </div>
                `;
                resultsGrid.appendChild(resultCard);
            }

            loadPreviewAnimation(fileName, animationData) {
                try {
                    const container = document.getElementById(`preview-${fileName}`);
                    if (!container || !window.lottie) return;

                    container.innerHTML = '';
                    
                    const animation = lottie.loadAnimation({
                        container: container,
                        renderer: 'svg',
                        loop: true,
                        autoplay: true,
                        animationData: animationData
                    });

                    this.lottieAnimations[fileName] = animation;

                    animation.addEventListener('data_failed', () => {
                        container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #dc3545;">❌ Ошибка загрузки</div>';
                    });

                } catch (error) {
                    console.error('Error loading preview animation:', error);
                }
            }

            showSummary(totalFiles, totalOriginalSize, totalOptimizedSize) {
                const summaryStats = document.getElementById('summaryStats');
                const averageCompression = totalOriginalSize > 0 ? ((totalOriginalSize - totalOptimizedSize) / totalOriginalSize) * 100 : 0;

                document.getElementById('totalFiles').textContent = totalFiles;
                document.getElementById('totalOriginalSize').textContent = this.formatFileSize(totalOriginalSize);
                document.getElementById('totalOptimizedSize').textContent = this.formatFileSize(totalOptimizedSize);
                document.getElementById('averageCompression').textContent = averageCompression.toFixed(1) + '%';

                summaryStats.style.display = 'block';
            }
        }

        // Глобальные функции
        const optimizer = new BatchLottieOptimizer();

        function startBatchOptimization() {
            optimizer.startBatchOptimization();
        }

        function selectFiles() {
            document.getElementById('fileInput').click();
        }

        function selectFolder() {
            document.getElementById('folderInput').click();
        }

        async function loadDefaultFiles() {
            // Загружаем файлы из mob/output как раньше
            const files = [];
            for (let i = 1; i <= 45; i++) {
                try {
                    const response = await fetch(`mob/output/${i}.json`);
                    if (response.ok) {
                        const blob = await response.blob();
                        const file = new File([blob], `${i}.json`, { type: 'application/json' });
                        files.push({ file, path: `mob/output/${i}.json` });
                    }
                } catch (error) {
                    console.warn(`Не удалось загрузить mob/output/${i}.json:`, error);
                }
            }

            if (files.length > 0) {
                optimizer.selectedFiles = files.map(f => f.file);
                optimizer.fileStructure.clear();
                files.forEach(f => optimizer.fileStructure.set(f.file, f.path));
                optimizer.updateFileStatus();
            } else {
                alert('Не удалось загрузить файлы из mob/output. Убедитесь, что папка существует и содержит JSON файлы.');
            }
        }

        function downloadSingle(fileName) {
            const result = optimizer.optimizationResults.find(r => r.fileName === fileName);
            if (result) {
                const dataStr = JSON.stringify(result.optimizedData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = fileName.replace('.json', '_optimized_1080x1920.json');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }
        }

        function downloadAllOptimized() {
            if (optimizer.optimizationResults.length === 0) {
                alert('Нет оптимизированных файлов для скачивания');
                return;
            }

            optimizer.optimizationResults.forEach((result, index) => {
                setTimeout(() => {
                    downloadSingle(result.fileName);
                }, index * 500);
            });
        }

        function clearResults() {
            document.getElementById('resultsGrid').innerHTML = '';
            document.getElementById('summaryStats').style.display = 'none';
            document.getElementById('progressSection').style.display = 'none';
            optimizer.optimizationResults = [];
            optimizer.lottieAnimations = {};
        }
    </script>
</body>
</html>
