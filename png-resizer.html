<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Compressor - Сжатие и изменение размера до 1080x1920</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .control-panel {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .settings {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .setting-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .setting-group label {
            font-weight: 600;
            color: #333;
        }

        .setting-group input,
        .setting-group select {
            padding: 10px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1rem;
        }

        .dropzone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9ff;
            margin: 20px 0;
        }

        .dropzone:hover,
        .dropzone.dragover {
            border-color: #764ba2;
            background: #f0f2ff;
            transform: scale(1.02);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }

        .progress-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .result-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .image-preview {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 15px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .file-input {
            display: none;
        }

        .stats {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 15px;
        }

        @media (max-width: 768px) {
            .settings {
                grid-template-columns: 1fr;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🗜️ Image Compressor</h1>
            <p>Сжатие и изменение размера изображений до 1080x1920 с реальным уменьшением файлов</p>
        </header>

        <div class="control-panel">
            <h2>Настройки изменения размера</h2>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; font-size: 0.9rem;">
                <strong>Режимы обработки:</strong><br>
                • <strong>Вписать</strong> - сохраняет пропорции, добавляет фон по краям<br>
                • <strong>Заполнить</strong> - сохраняет пропорции, обрезает лишнее<br>
                • <strong>Растянуть</strong> - может исказить изображение
            </div>
            <div class="settings">
                <div class="setting-group">
                    <label>Целевая ширина:</label>
                    <input type="number" id="targetWidth" value="1080" min="100" max="4000">
                </div>
                <div class="setting-group">
                    <label>Целевая высота:</label>
                    <input type="number" id="targetHeight" value="1920" min="100" max="4000">
                </div>
                <div class="setting-group">
                    <label>Режим изменения размера:</label>
                    <select id="resizeMode">
                        <option value="fit" selected>Вписать с сохранением пропорций</option>
                        <option value="fill">Заполнить с обрезкой (сохранить пропорции)</option>
                        <option value="stretch">Растянуть до размера (искажение)</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>Цвет фона (для режима "Вписать"):</label>
                    <input type="color" id="backgroundColor" value="#ffffff">
                </div>
                <div class="setting-group">
                    <label>Формат вывода:</label>
                    <select id="outputFormat">
                        <option value="png">PNG (без потерь)</option>
                        <option value="jpeg" selected>JPEG (с сжатием)</option>
                        <option value="webp">WebP (современный формат)</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label>Качество сжатия (0.1-1.0):</label>
                    <input type="number" id="compressionQuality" value="0.7" min="0.1" max="1.0" step="0.1">
                </div>
            </div>

            <div class="dropzone" id="dropzone">
                <div>
                    <h3>📁 Перетащите изображения сюда</h3>
                    <p>или нажмите для выбора файлов</p>
                    <p style="font-size: 0.9rem; color: #999;">
                        Поддерживаются файлы .png, .jpg, .jpeg, .webp
                    </p>
                </div>
            </div>

            <input
                id="fileInput"
                type="file"
                accept=".png,.jpg,.jpeg,.webp,image/png,image/jpeg,image/webp"
                multiple
                class="file-input"
            />

            <div style="text-align: center;">
                <button class="btn btn-primary" onclick="processAllImages()">
                    🚀 Обработать все изображения
                </button>
                <button class="btn btn-secondary" onclick="downloadAllProcessed()">
                    📥 Скачать все обработанные
                </button>
                <button class="btn btn-secondary" onclick="clearResults()">
                    🗑️ Очистить результаты
                </button>
            </div>
        </div>

        <div class="progress-section" id="progressSection">
            <h3>Прогресс обработки</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">Готов к запуску...</div>
        </div>

        <div class="results-grid" id="resultsGrid"></div>
    </div>

    <script>
        class PNGResizer {
            constructor() {
                this.processedImages = [];
                this.selectedFiles = [];
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                const dropzone = document.getElementById('dropzone');
                const fileInput = document.getElementById('fileInput');

                // Drag and drop events
                dropzone.addEventListener('click', () => fileInput.click());
                dropzone.addEventListener('dragover', this.handleDragOver.bind(this));
                dropzone.addEventListener('dragleave', this.handleDragLeave.bind(this));
                dropzone.addEventListener('drop', this.handleDrop.bind(this));

                // File input change
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.remove('dragover');

                const files = Array.from(e.dataTransfer.files);
                const imageFiles = files.filter(file =>
                    file.type.startsWith('image/') ||
                    /\.(png|jpg|jpeg|webp)$/i.test(file.name)
                );

                if (imageFiles.length > 0) {
                    this.selectedFiles = imageFiles;
                    this.showSelectedFiles();
                } else {
                    alert('Пожалуйста, выберите файлы изображений');
                }
            }

            handleFileSelect(e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    this.selectedFiles = files;
                    this.showSelectedFiles();
                }
            }

            showSelectedFiles() {
                const dropzone = document.getElementById('dropzone');
                dropzone.innerHTML = `
                    <div>
                        <h3>✅ Выбрано файлов: ${this.selectedFiles.length}</h3>
                        <p>Нажмите "Обработать все изображения" для начала</p>
                        <button class="btn btn-secondary" onclick="resizer.selectNewFiles()" style="margin-top: 10px;">
                            Выбрать другие файлы
                        </button>
                    </div>
                `;
            }

            selectNewFiles() {
                this.selectedFiles = [];
                const dropzone = document.getElementById('dropzone');
                dropzone.innerHTML = `
                    <div>
                        <h3>📁 Перетащите изображения сюда</h3>
                        <p>или нажмите для выбора файлов</p>
                        <p style="font-size: 0.9rem; color: #999;">
                            Поддерживаются файлы .png, .jpg, .jpeg, .webp
                        </p>
                    </div>
                `;
            }

            async processAllImages() {
                if (this.selectedFiles.length === 0) {
                    alert('Сначала выберите файлы изображений');
                    return;
                }

                const progressSection = document.getElementById('progressSection');
                const resultsGrid = document.getElementById('resultsGrid');
                
                progressSection.style.display = 'block';
                resultsGrid.innerHTML = '';
                this.processedImages = [];

                const targetWidth = parseInt(document.getElementById('targetWidth').value);
                const targetHeight = parseInt(document.getElementById('targetHeight').value);
                const resizeMode = document.getElementById('resizeMode').value;
                const backgroundColor = document.getElementById('backgroundColor').value;
                const outputFormat = document.getElementById('outputFormat').value;
                const compressionQuality = parseFloat(document.getElementById('compressionQuality').value);

                for (let i = 0; i < this.selectedFiles.length; i++) {
                    const file = this.selectedFiles[i];
                    
                    this.updateProgress(i, this.selectedFiles.length, `Обрабатываем ${file.name}...`);
                    
                    try {
                        const processedImage = await this.resizeImage(file, targetWidth, targetHeight, resizeMode, backgroundColor, outputFormat, compressionQuality);
                        this.processedImages.push(processedImage);
                        this.displayResult(processedImage);
                    } catch (error) {
                        console.error(`Ошибка при обработке ${file.name}:`, error);
                        this.displayError(file.name, error.message);
                    }

                    // Небольшая задержка
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                this.updateProgress(this.selectedFiles.length, this.selectedFiles.length, 'Обработка завершена!');
            }

            async resizeImage(file, targetWidth, targetHeight, resizeMode, backgroundColor, outputFormat, compressionQuality) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');
                            
                            if (!ctx) {
                                reject(new Error('Cannot get canvas context'));
                                return;
                            }

                            canvas.width = targetWidth;
                            canvas.height = targetHeight;

                            let drawWidth, drawHeight, drawX, drawY;
                            let sourceX = 0, sourceY = 0, sourceWidth = img.width, sourceHeight = img.height;

                            switch (resizeMode) {
                                case 'stretch':
                                    // Растягиваем до целевого размера (искажение)
                                    drawWidth = targetWidth;
                                    drawHeight = targetHeight;
                                    drawX = 0;
                                    drawY = 0;
                                    break;

                                case 'fit':
                                    // Вписываем с сохранением пропорций
                                    const fitRatio = Math.min(targetWidth / img.width, targetHeight / img.height);
                                    drawWidth = Math.round(img.width * fitRatio);
                                    drawHeight = Math.round(img.height * fitRatio);
                                    drawX = Math.round((targetWidth - drawWidth) / 2);
                                    drawY = Math.round((targetHeight - drawHeight) / 2);

                                    // Заливаем фон выбранным цветом
                                    ctx.fillStyle = backgroundColor;
                                    ctx.fillRect(0, 0, targetWidth, targetHeight);
                                    break;

                                case 'fill':
                                    // Заполняем с обрезкой (crop to fit)
                                    const fillRatio = Math.max(targetWidth / img.width, targetHeight / img.height);

                                    // Размеры после масштабирования
                                    const scaledWidth = img.width * fillRatio;
                                    const scaledHeight = img.height * fillRatio;

                                    // Если изображение больше целевого размера после масштабирования, обрезаем
                                    if (scaledWidth > targetWidth) {
                                        // Обрезаем по ширине
                                        sourceWidth = targetWidth / fillRatio;
                                        sourceX = (img.width - sourceWidth) / 2;
                                    }

                                    if (scaledHeight > targetHeight) {
                                        // Обрезаем по высоте
                                        sourceHeight = targetHeight / fillRatio;
                                        sourceY = (img.height - sourceHeight) / 2;
                                    }

                                    drawWidth = targetWidth;
                                    drawHeight = targetHeight;
                                    drawX = 0;
                                    drawY = 0;
                                    break;
                            }

                            // Рисуем изображение с правильными параметрами
                            ctx.drawImage(img, sourceX, sourceY, sourceWidth, sourceHeight, drawX, drawY, drawWidth, drawHeight);

                            // Определяем MIME тип и расширение файла
                            let mimeType, fileExtension, dataUrl;
                            switch (outputFormat) {
                                case 'jpeg':
                                    mimeType = 'image/jpeg';
                                    fileExtension = '.jpg';
                                    dataUrl = canvas.toDataURL('image/jpeg', compressionQuality);
                                    break;
                                case 'webp':
                                    mimeType = 'image/webp';
                                    fileExtension = '.webp';
                                    dataUrl = canvas.toDataURL('image/webp', compressionQuality);
                                    break;
                                default: // png
                                    mimeType = 'image/png';
                                    fileExtension = '.png';
                                    dataUrl = canvas.toDataURL('image/png');
                                    break;
                            }

                            canvas.toBlob((blob) => {
                                if (blob) {
                                    const compressionRatio = ((file.size - blob.size) / file.size * 100).toFixed(1);
                                    const processedImage = {
                                        originalName: file.name,
                                        originalSize: file.size,
                                        newSize: blob.size,
                                        blob: blob,
                                        dataUrl: dataUrl,
                                        dimensions: `${targetWidth}x${targetHeight}`,
                                        mode: resizeMode,
                                        format: outputFormat,
                                        quality: compressionQuality,
                                        compressionRatio: compressionRatio,
                                        fileExtension: fileExtension
                                    };
                                    resolve(processedImage);
                                } else {
                                    reject(new Error('Failed to create blob'));
                                }
                            }, mimeType, outputFormat === 'png' ? undefined : compressionQuality);

                        } catch (error) {
                            reject(error);
                        }
                    };

                    img.onerror = () => reject(new Error('Failed to load image'));
                    img.src = URL.createObjectURL(file);
                });
            }

            updateProgress(current, total, message) {
                const progress = (current / total) * 100;
                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('progressText').textContent = `${message} (${current}/${total})`;
            }

            displayResult(processedImage) {
                const resultsGrid = document.getElementById('resultsGrid');
                
                const resultCard = document.createElement('div');
                resultCard.className = 'result-card';
                resultCard.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 10px;">${processedImage.originalName}</div>
                    <div class="image-preview">
                        <img src="${processedImage.dataUrl}" alt="Processed image">
                    </div>
                    <div class="stats">
                        <div>Исходный размер: ${this.formatFileSize(processedImage.originalSize)}</div>
                        <div>Новый размер: ${this.formatFileSize(processedImage.newSize)}</div>
                        <div>Сжатие: <strong style="color: ${processedImage.compressionRatio > 0 ? 'green' : 'orange'}">${processedImage.compressionRatio}%</strong></div>
                        <div>Разрешение: ${processedImage.dimensions}</div>
                        <div>Формат: ${processedImage.format.toUpperCase()} (качество: ${processedImage.quality})</div>
                        <div>Режим: ${this.getModeText(processedImage.mode)}</div>
                    </div>
                    <button class="btn btn-primary" onclick="resizer.downloadSingle('${processedImage.originalName}')">
                        📥 Скачать
                    </button>
                `;
                
                resultsGrid.appendChild(resultCard);
            }

            displayError(fileName, errorMessage) {
                const resultsGrid = document.getElementById('resultsGrid');
                const resultCard = document.createElement('div');
                resultCard.className = 'result-card';
                resultCard.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 10px;">${fileName}</div>
                    <div style="color: #dc3545; padding: 20px; text-align: center;">
                        ❌ Ошибка: ${errorMessage}
                    </div>
                `;
                resultsGrid.appendChild(resultCard);
            }

            getModeText(mode) {
                switch (mode) {
                    case 'stretch': return 'Растянуть (с искажением)';
                    case 'fit': return 'Вписать (с сохранением пропорций)';
                    case 'fill': return 'Заполнить (обрезка с сохранением пропорций)';
                    default: return mode;
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            downloadSingle(originalName) {
                const processedImage = this.processedImages.find(img => img.originalName === originalName);
                if (processedImage) {
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(processedImage.blob);
                    const baseName = originalName.replace(/\.[^/.]+$/, ""); // Убираем расширение
                    link.download = `${baseName}_1080x1920${processedImage.fileExtension}`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(link.href);
                }
            }

            downloadAllProcessed() {
                if (this.processedImages.length === 0) {
                    alert('Нет обработанных изображений для скачивания');
                    return;
                }

                this.processedImages.forEach((processedImage, index) => {
                    setTimeout(() => {
                        this.downloadSingle(processedImage.originalName);
                    }, index * 500);
                });
            }

            clearResults() {
                document.getElementById('resultsGrid').innerHTML = '';
                document.getElementById('progressSection').style.display = 'none';
                this.processedImages = [];
                this.selectNewFiles();
            }
        }

        // Глобальные функции
        const resizer = new PNGResizer();

        function processAllImages() {
            resizer.processAllImages();
        }

        function downloadAllProcessed() {
            resizer.downloadAllProcessed();
        }

        function clearResults() {
            resizer.clearResults();
        }
    </script>
</body>
</html>
