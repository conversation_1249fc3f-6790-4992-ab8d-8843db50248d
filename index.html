<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lottie Optimizer - Оптимизатор Lottie анимаций</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .upload-area {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .dropzone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9ff;
        }

        .dropzone:hover,
        .dropzone.dragover {
            border-color: #764ba2;
            background: #f0f2ff;
            transform: scale(1.02);
        }

        .dropzone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .upload-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .dropzone h3 {
            font-size: 1.5rem;
            color: #333;
            margin: 0;
        }

        .dropzone p {
            color: #666;
            font-size: 1rem;
            margin: 0;
        }

        .file-input {
            display: none;
        }

        .results {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            display: none;
        }

        .results h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
        }

        .stat-card p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .loading {
            display: none;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #667eea;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            color: #c53030;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        .success {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
            color: #2f855a;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            display: none;
        }

        .json-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            overflow: auto;
            max-height: 400px;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .upload-area {
                padding: 20px;
            }

            .dropzone {
                padding: 40px 15px;
            }

            .stats {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Lottie Optimizer</h1>
            <p>Оптимизируйте ваши Lottie анимации с сохранением качества</p>
        </header>

        <div class="upload-area" id="uploadArea">
            <div class="dropzone" id="dropzone">
                <div class="dropzone-content">
                    <div class="upload-icon">📁</div>
                    <h3>Перетащите Lottie файл сюда</h3>
                    <p>или нажмите для выбора файла</p>
                    <p style="font-size: 0.9rem; color: #999;">
                        Поддерживаются файлы .json
                    </p>
                </div>
            </div>

            <input
                id="fileInput"
                type="file"
                accept=".json,application/json"
                class="file-input"
            />
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <span>Оптимизируем ваш файл...</span>
        </div>

        <div class="error" id="error"></div>
        <div class="success" id="success"></div>

        <div class="results" id="results">
            <h2>Результаты оптимизации</h2>

            <div class="stats">
                <div class="stat-card">
                    <h3 id="originalSize">0 KB</h3>
                    <p>Исходный размер</p>
                </div>

                <div class="stat-card">
                    <h3 id="optimizedSize">0 KB</h3>
                    <p>Оптимизированный размер</p>
                </div>

                <div class="stat-card">
                    <h3 id="compressionRatio">0%</h3>
                    <p>Сжатие</p>
                </div>

                <div class="stat-card">
                    <h3 id="savedSize">0 KB</h3>
                    <p>Сэкономлено</p>
                </div>
            </div>

            <div class="actions">
                <button class="btn btn-primary" id="downloadBtn">
                    📥 Скачать оптимизированный файл
                </button>

                <button class="btn btn-secondary" id="copyBtn">
                    📋 Копировать JSON
                </button>

                <button class="btn btn-secondary" id="resetBtn">
                    🔄 Оптимизировать другой файл
                </button>
            </div>

            <details style="margin-top: 20px;">
                <summary style="cursor: pointer; font-weight: bold; margin-bottom: 10px;">
                    Просмотр оптимизированного JSON
                </summary>
                <div class="json-preview" id="jsonPreview"></div>
            </details>
        </div>
    </div>

    <script>
        // Lottie Optimizer JavaScript Implementation
        class LottieOptimizer {
            constructor() {
                this.qualityThreshold = 0.6;
                this.initializeEventListeners();
            }

            initializeEventListeners() {
                const dropzone = document.getElementById('dropzone');
                const fileInput = document.getElementById('fileInput');
                const downloadBtn = document.getElementById('downloadBtn');
                const copyBtn = document.getElementById('copyBtn');
                const resetBtn = document.getElementById('resetBtn');

                // Drag and drop events
                dropzone.addEventListener('click', () => fileInput.click());
                dropzone.addEventListener('dragover', this.handleDragOver.bind(this));
                dropzone.addEventListener('dragleave', this.handleDragLeave.bind(this));
                dropzone.addEventListener('drop', this.handleDrop.bind(this));

                // File input change
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));

                // Button events
                downloadBtn.addEventListener('click', this.downloadOptimized.bind(this));
                copyBtn.addEventListener('click', this.copyToClipboard.bind(this));
                resetBtn.addEventListener('click', this.reset.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.remove('dragover');

                const files = Array.from(e.dataTransfer.files);
                const lottieFile = files.find(file =>
                    file.type === 'application/json' ||
                    file.name.toLowerCase().endsWith('.json')
                );

                if (lottieFile) {
                    this.processFile(lottieFile);
                } else {
                    this.showError('Пожалуйста, выберите JSON файл с Lottie анимацией');
                }
            }

            handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) {
                    this.processFile(file);
                }
            }

            async processFile(file) {
                this.showLoading(true);
                this.hideError();
                this.hideSuccess();

                try {
                    const text = await file.text();
                    const lottieData = JSON.parse(text);

                    const result = await this.optimizeLottie(lottieData);
                    this.showResults(result, file.name);
                    this.showSuccess('Файл успешно оптимизирован!');
                } catch (error) {
                    this.showError('Ошибка при обработке файла: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            async optimizeLottie(lottieData) {
                const originalJson = JSON.stringify(lottieData);
                const originalSize = new Blob([originalJson]).size;

                // Create a copy for optimization
                const optimized = JSON.parse(originalJson);

                // Optimize assets
                await this.optimizeAssets(optimized.assets || []);

                // Optimize JSON structure
                this.optimizeJsonStructure(optimized);

                // Round numbers
                this.roundNumbers(optimized);

                const optimizedJson = JSON.stringify(optimized);
                const optimizedSize = new Blob([optimizedJson]).size;
                const compressionRatio = (originalSize - optimizedSize) / originalSize;

                return {
                    optimized,
                    originalSize,
                    optimizedSize,
                    compressionRatio,
                    originalFileName: ''
                };
            }

            async optimizeAssets(assets) {
                for (const asset of assets) {
                    if (asset.p && this.isBase64Image(asset.p)) {
                        try {
                            asset.p = await this.compressBase64Image(asset.p);
                        } catch (error) {
                            console.warn('Failed to compress image:', error);
                        }
                    }
                }
            }

            isBase64Image(str) {
                return str.startsWith('data:image/');
            }

            async compressBase64Image(base64) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        if (!ctx) {
                            reject(new Error('Cannot get canvas context'));
                            return;
                        }

                        // Determine new dimensions while maintaining aspect ratio
                        const maxWidth = 1024;
                        const maxHeight = 1024;
                        let { width, height } = img;

                        if (width > maxWidth || height > maxHeight) {
                            const ratio = Math.min(maxWidth / width, maxHeight / height);
                            width *= ratio;
                            height *= ratio;
                        }

                        canvas.width = width;
                        canvas.height = height;

                        // Draw image with new dimensions
                        ctx.drawImage(img, 0, 0, width, height);

                        // Convert back to base64 with quality 0.8
                        const compressedBase64 = canvas.toDataURL('image/jpeg', 0.8);
                        resolve(compressedBase64);
                    };

                    img.onerror = () => reject(new Error('Failed to load image'));
                    img.src = base64;
                });
            }

            optimizeJsonStructure(lottie) {
                this.removeUnusedProperties(lottie);
                if (lottie.layers) {
                    this.optimizeLayers(lottie.layers);
                }
            }

            removeUnusedProperties(obj) {
                const unnecessaryProps = ['nm', 'mn', 'hd'];

                if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
                    for (const prop of unnecessaryProps) {
                        if (prop in obj) {
                            delete obj[prop];
                        }
                    }

                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            this.removeUnusedProperties(obj[key]);
                        }
                    }
                } else if (Array.isArray(obj)) {
                    obj.forEach(item => this.removeUnusedProperties(item));
                }
            }

            optimizeLayers(layers) {
                if (!Array.isArray(layers)) return;

                layers.forEach(layer => {
                    if (layer.hd === true) return;

                    if (layer.ks) {
                        this.optimizeKeyframes(layer.ks);
                    }

                    if (layer.layers) {
                        this.optimizeLayers(layer.layers);
                    }
                });
            }

            optimizeKeyframes(ks) {
                if (typeof ks === 'object' && ks !== null) {
                    for (const key in ks) {
                        if (ks[key] && typeof ks[key] === 'object') {
                            if (ks[key].k && Array.isArray(ks[key].k)) {
                                ks[key].k = this.removeDuplicateKeyframes(ks[key].k);
                            }
                            this.optimizeKeyframes(ks[key]);
                        }
                    }
                }
            }

            removeDuplicateKeyframes(keyframes) {
                const unique = [];
                let lastKeyframe = null;

                for (const kf of keyframes) {
                    if (!lastKeyframe || !this.areKeyframesEqual(kf, lastKeyframe)) {
                        unique.push(kf);
                        lastKeyframe = kf;
                    }
                }

                return unique;
            }

            areKeyframesEqual(kf1, kf2) {
                return JSON.stringify(kf1.s) === JSON.stringify(kf2.s) &&
                       JSON.stringify(kf1.e) === JSON.stringify(kf2.e);
            }

            roundNumbers(obj, precision = 3) {
                if (typeof obj === 'number') {
                    return Math.round(obj * Math.pow(10, precision)) / Math.pow(10, precision);
                }

                if (Array.isArray(obj)) {
                    for (let i = 0; i < obj.length; i++) {
                        if (typeof obj[i] === 'number') {
                            obj[i] = Math.round(obj[i] * Math.pow(10, precision)) / Math.pow(10, precision);
                        } else {
                            this.roundNumbers(obj[i], precision);
                        }
                    }
                } else if (typeof obj === 'object' && obj !== null) {
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            if (typeof obj[key] === 'number') {
                                obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
                            } else {
                                this.roundNumbers(obj[key], precision);
                            }
                        }
                    }
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            showResults(result, fileName) {
                this.currentResult = result;
                this.currentFileName = fileName;

                document.getElementById('originalSize').textContent = this.formatFileSize(result.originalSize);
                document.getElementById('optimizedSize').textContent = this.formatFileSize(result.optimizedSize);
                document.getElementById('compressionRatio').textContent = Math.round(result.compressionRatio * 100) + '%';
                document.getElementById('savedSize').textContent = this.formatFileSize(result.originalSize - result.optimizedSize);
                document.getElementById('jsonPreview').textContent = JSON.stringify(result.optimized, null, 2);

                document.getElementById('uploadArea').style.display = 'none';
                document.getElementById('results').style.display = 'block';
            }

            downloadOptimized() {
                if (!this.currentResult) return;

                const dataStr = JSON.stringify(this.currentResult.optimized, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = this.currentFileName.replace('.json', '_optimized.json');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }

            async copyToClipboard() {
                if (!this.currentResult) return;

                try {
                    await navigator.clipboard.writeText(JSON.stringify(this.currentResult.optimized));
                    this.showSuccess('JSON скопирован в буфер обмена!');
                } catch (err) {
                    console.error('Ошибка копирования:', err);
                    this.showError('Не удалось скопировать в буфер обмена');
                }
            }

            reset() {
                document.getElementById('uploadArea').style.display = 'block';
                document.getElementById('results').style.display = 'none';
                document.getElementById('fileInput').value = '';
                this.hideError();
                this.hideSuccess();
                this.currentResult = null;
                this.currentFileName = '';
            }

            showLoading(show) {
                document.getElementById('loading').style.display = show ? 'flex' : 'none';
            }

            showError(message) {
                const errorEl = document.getElementById('error');
                errorEl.textContent = message;
                errorEl.style.display = 'block';
            }

            hideError() {
                document.getElementById('error').style.display = 'none';
            }

            showSuccess(message) {
                const successEl = document.getElementById('success');
                successEl.textContent = message;
                successEl.style.display = 'block';
                setTimeout(() => this.hideSuccess(), 3000);
            }

            hideSuccess() {
                document.getElementById('success').style.display = 'none';
            }
        }

        // Initialize the optimizer when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new LottieOptimizer();
        });
    </script>
</body>
</html>
