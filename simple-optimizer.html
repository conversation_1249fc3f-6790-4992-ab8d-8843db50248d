<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Простой оптимизатор</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: #2c3e50;
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: #34495e;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #3498db;
        }

        .dropzone {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 60px 20px;
            margin: 30px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(52, 152, 219, 0.1);
        }

        .dropzone:hover,
        .dropzone.dragover {
            border-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
            transform: scale(1.02);
        }

        .dropzone h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
        }

        .btn-success:hover {
            background: #229954;
        }

        .results {
            margin-top: 30px;
            padding: 20px;
            background: rgba(39, 174, 96, 0.1);
            border-radius: 10px;
            display: none;
        }

        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .stat {
            background: rgba(52, 152, 219, 0.2);
            padding: 15px;
            border-radius: 8px;
        }

        .stat h4 {
            color: #3498db;
            margin-bottom: 5px;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(52, 152, 219, 0.3);
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .compression-ratio {
            font-size: 2rem;
            font-weight: bold;
            color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Оптимизатор</h1>
        
        <div class="dropzone" id="dropzone">
            <h3>📁 Перетащите файл сюда</h3>
            <p>или нажмите для выбора</p>
            <p style="font-size: 0.9rem; opacity: 0.7; margin-top: 10px;">
                Поддерживаются: JSON, PNG, JPG, WEBP
            </p>
        </div>

        <input type="file" id="fileInput" class="file-input" accept=".json,.png,.jpg,.jpeg,.webp">

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Оптимизируем файл...</p>
        </div>

        <div class="results" id="results">
            <h3>✅ Готово!</h3>
            <div class="stats">
                <div class="stat">
                    <h4>Исходный размер</h4>
                    <div id="originalSize">0 KB</div>
                </div>
                <div class="stat">
                    <h4>Новый размер</h4>
                    <div id="newSize">0 KB</div>
                </div>
            </div>
            <div class="compression-ratio" id="compressionRatio">0%</div>
            <p>сжатие</p>
            <button class="btn btn-success" id="downloadBtn">📥 Скачать</button>
        </div>
    </div>

    <script>
        class SimpleOptimizer {
            constructor() {
                this.currentFile = null;
                this.optimizedData = null;
                this.initEvents();
            }

            initEvents() {
                const dropzone = document.getElementById('dropzone');
                const fileInput = document.getElementById('fileInput');
                const downloadBtn = document.getElementById('downloadBtn');

                dropzone.addEventListener('click', () => fileInput.click());
                dropzone.addEventListener('dragover', this.handleDragOver.bind(this));
                dropzone.addEventListener('dragleave', this.handleDragLeave.bind(this));
                dropzone.addEventListener('drop', this.handleDrop.bind(this));
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                downloadBtn.addEventListener('click', this.download.bind(this));
            }

            handleDragOver(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.add('dragover');
            }

            handleDragLeave(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.remove('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                document.getElementById('dropzone').classList.remove('dragover');
                const file = e.dataTransfer.files[0];
                if (file) this.processFile(file);
            }

            handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) this.processFile(file);
            }

            async processFile(file) {
                this.currentFile = file;
                this.showLoading(true);

                try {
                    if (file.name.toLowerCase().endsWith('.json')) {
                        await this.optimizeJSON(file);
                    } else {
                        await this.optimizeImage(file);
                    }
                } catch (error) {
                    alert('Ошибка: ' + error.message);
                } finally {
                    this.showLoading(false);
                }
            }

            async optimizeJSON(file) {
                try {
                    const text = await file.text();
                    const data = JSON.parse(text);

                    // Оптимизируем assets если есть
                    if (data.assets && Array.isArray(data.assets)) {
                        for (let asset of data.assets) {
                            if (asset.p && asset.p.startsWith('data:image/')) {
                                asset.p = await this.compressBase64Image(asset.p);
                            }
                        }
                    }

                    // Простая оптимизация JSON
                    const optimized = this.optimizeJSONData(data);

                    const originalSize = file.size;
                    const optimizedBlob = new Blob([JSON.stringify(optimized)], {type: 'application/json'});
                    const newSize = optimizedBlob.size;

                    this.optimizedData = {
                        blob: optimizedBlob,
                        filename: file.name.replace('.json', '_optimized.json')
                    };

                    this.showResults(originalSize, newSize);
                } catch (error) {
                    console.error('JSON optimization error:', error);
                    throw new Error('Не удалось обработать JSON файл');
                }
            }

            async optimizeImage(file) {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                return new Promise((resolve, reject) => {
                    img.onload = () => {
                        try {
                            // Изменяем размер до 1424x2532
                            canvas.width = 1424;
                            canvas.height = 2532;

                            // Вписываем с сохранением пропорций
                            const ratio = Math.min(1424 / img.width, 2532 / img.height);
                            const width = img.width * ratio;
                            const height = img.height * ratio;
                            const x = (1424 - width) / 2;
                            const y = (2532 - height) / 2;

                            ctx.fillStyle = '#ffffff';
                            ctx.fillRect(0, 0, 1424, 2532);
                            ctx.drawImage(img, x, y, width, height);

                            canvas.toBlob((blob) => {
                                if (blob) {
                                    this.optimizedData = {
                                        blob: blob,
                                        filename: file.name.replace(/\.[^/.]+$/, '_1424x2532.jpg')
                                    };
                                    this.showResults(file.size, blob.size);
                                    resolve();
                                } else {
                                    reject(new Error('Не удалось создать изображение'));
                                }
                            }, 'image/jpeg', 0.7);
                        } catch (error) {
                            reject(error);
                        }
                    };

                    img.onerror = () => reject(new Error('Не удалось загрузить изображение'));
                    img.src = URL.createObjectURL(file);
                });
            }

            async compressBase64Image(base64) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        const maxSize = 1024;
                        let { width, height } = img;

                        if (width > maxSize || height > maxSize) {
                            const ratio = Math.min(maxSize / width, maxSize / height);
                            width *= ratio;
                            height *= ratio;
                        }

                        canvas.width = width;
                        canvas.height = height;
                        ctx.drawImage(img, 0, 0, width, height);

                        const compressed = canvas.toDataURL('image/jpeg', 0.7);
                        resolve(compressed);
                    };

                    img.onerror = () => resolve(base64); // Возвращаем оригинал если ошибка
                    img.src = base64;
                });
            }

            optimizeJSONData(data) {
                try {
                    const optimized = JSON.parse(JSON.stringify(data));

                    // Удаляем ненужные свойства
                    this.removeUnusedProps(optimized);

                    // Округляем числа
                    this.roundNumbers(optimized);

                    return optimized;
                } catch (error) {
                    console.error('JSON data optimization error:', error);
                    return data; // Возвращаем оригинал если ошибка
                }
            }

            removeUnusedProps(obj) {
                if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
                    delete obj.nm;
                    delete obj.mn;
                    delete obj.hd;

                    for (let key in obj) {
                        if (obj[key] && typeof obj[key] === 'object') {
                            this.removeUnusedProps(obj[key]);
                        }
                    }
                } else if (Array.isArray(obj)) {
                    obj.forEach(item => this.removeUnusedProps(item));
                }
            }

            roundNumbers(obj) {
                if (typeof obj === 'number') {
                    return Math.round(obj * 1000) / 1000;
                }

                if (Array.isArray(obj)) {
                    for (let i = 0; i < obj.length; i++) {
                        if (typeof obj[i] === 'number') {
                            obj[i] = Math.round(obj[i] * 1000) / 1000;
                        } else if (typeof obj[i] === 'object') {
                            this.roundNumbers(obj[i]);
                        }
                    }
                } else if (typeof obj === 'object' && obj !== null) {
                    for (let key in obj) {
                        if (typeof obj[key] === 'number') {
                            obj[key] = Math.round(obj[key] * 1000) / 1000;
                        } else if (typeof obj[key] === 'object') {
                            this.roundNumbers(obj[key]);
                        }
                    }
                }
            }

            showResults(originalSize, newSize) {
                const compression = Math.round(((originalSize - newSize) / originalSize) * 100);
                
                document.getElementById('originalSize').textContent = this.formatSize(originalSize);
                document.getElementById('newSize').textContent = this.formatSize(newSize);
                document.getElementById('compressionRatio').textContent = compression + '%';
                document.getElementById('results').style.display = 'block';
            }

            formatSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
            }

            showLoading(show) {
                document.getElementById('loading').style.display = show ? 'block' : 'none';
                document.getElementById('results').style.display = 'none';
            }

            download() {
                if (!this.optimizedData) return;
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(this.optimizedData.blob);
                link.download = this.optimizedData.filename;
                link.click();
                URL.revokeObjectURL(link.href);
            }
        }

        new SimpleOptimizer();
    </script>
</body>
</html>
