<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тестирование Lottie Optimizer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .result-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .stats {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>Тестирование Lottie Optimizer</h1>
    
    <div class="test-container">
        <h2>Автоматическое тестирование с существующими файлами</h2>
        <p>Этот инструмент протестирует оптимизатор на всех доступных Lottie файлах в папке mob/output</p>
        
        <button class="btn" onclick="runBatchTest()">Запустить пакетное тестирование</button>
        <button class="btn" onclick="clearResults()">Очистить результаты</button>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        <div id="progressText">Готов к тестированию</div>
    </div>

    <div id="results"></div>

    <script>
        // Import the LottieOptimizer class from the main page
        class LottieOptimizer {
            constructor() {
                this.qualityThreshold = 0.6;
            }

            async optimizeLottie(lottieData) {
                const originalJson = JSON.stringify(lottieData);
                const originalSize = new Blob([originalJson]).size;

                const optimized = JSON.parse(originalJson);

                await this.optimizeAssets(optimized.assets || []);
                this.optimizeJsonStructure(optimized);
                this.roundNumbers(optimized);

                const optimizedJson = JSON.stringify(optimized);
                const optimizedSize = new Blob([optimizedJson]).size;
                const compressionRatio = (originalSize - optimizedSize) / originalSize;

                return {
                    optimized,
                    originalSize,
                    optimizedSize,
                    compressionRatio
                };
            }

            async optimizeAssets(assets) {
                for (const asset of assets) {
                    if (asset.p && this.isBase64Image(asset.p)) {
                        try {
                            asset.p = await this.compressBase64Image(asset.p);
                        } catch (error) {
                            console.warn('Failed to compress image:', error);
                        }
                    }
                }
            }

            isBase64Image(str) {
                return str.startsWith('data:image/');
            }

            async compressBase64Image(base64) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        
                        if (!ctx) {
                            reject(new Error('Cannot get canvas context'));
                            return;
                        }

                        const maxWidth = 1024;
                        const maxHeight = 1024;
                        let { width, height } = img;

                        if (width > maxWidth || height > maxHeight) {
                            const ratio = Math.min(maxWidth / width, maxHeight / height);
                            width *= ratio;
                            height *= ratio;
                        }

                        canvas.width = width;
                        canvas.height = height;
                        ctx.drawImage(img, 0, 0, width, height);

                        const compressedBase64 = canvas.toDataURL('image/jpeg', 0.8);
                        resolve(compressedBase64);
                    };

                    img.onerror = () => reject(new Error('Failed to load image'));
                    img.src = base64;
                });
            }

            optimizeJsonStructure(lottie) {
                this.removeUnusedProperties(lottie);
                if (lottie.layers) {
                    this.optimizeLayers(lottie.layers);
                }
            }

            removeUnusedProperties(obj) {
                const unnecessaryProps = ['nm', 'mn', 'hd'];
                
                if (typeof obj === 'object' && obj !== null && !Array.isArray(obj)) {
                    for (const prop of unnecessaryProps) {
                        if (prop in obj) {
                            delete obj[prop];
                        }
                    }

                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            this.removeUnusedProperties(obj[key]);
                        }
                    }
                } else if (Array.isArray(obj)) {
                    obj.forEach(item => this.removeUnusedProperties(item));
                }
            }

            optimizeLayers(layers) {
                if (!Array.isArray(layers)) return;

                layers.forEach(layer => {
                    if (layer.hd === true) return;

                    if (layer.ks) {
                        this.optimizeKeyframes(layer.ks);
                    }

                    if (layer.layers) {
                        this.optimizeLayers(layer.layers);
                    }
                });
            }

            optimizeKeyframes(ks) {
                if (typeof ks === 'object' && ks !== null) {
                    for (const key in ks) {
                        if (ks[key] && typeof ks[key] === 'object') {
                            if (ks[key].k && Array.isArray(ks[key].k)) {
                                ks[key].k = this.removeDuplicateKeyframes(ks[key].k);
                            }
                            this.optimizeKeyframes(ks[key]);
                        }
                    }
                }
            }

            removeDuplicateKeyframes(keyframes) {
                const unique = [];
                let lastKeyframe = null;

                for (const kf of keyframes) {
                    if (!lastKeyframe || !this.areKeyframesEqual(kf, lastKeyframe)) {
                        unique.push(kf);
                        lastKeyframe = kf;
                    }
                }

                return unique;
            }

            areKeyframesEqual(kf1, kf2) {
                return JSON.stringify(kf1.s) === JSON.stringify(kf2.s) &&
                       JSON.stringify(kf1.e) === JSON.stringify(kf2.e);
            }

            roundNumbers(obj, precision = 3) {
                if (typeof obj === 'number') {
                    return Math.round(obj * Math.pow(10, precision)) / Math.pow(10, precision);
                }

                if (Array.isArray(obj)) {
                    for (let i = 0; i < obj.length; i++) {
                        if (typeof obj[i] === 'number') {
                            obj[i] = Math.round(obj[i] * Math.pow(10, precision)) / Math.pow(10, precision);
                        } else {
                            this.roundNumbers(obj[i], precision);
                        }
                    }
                } else if (typeof obj === 'object' && obj !== null) {
                    for (const key in obj) {
                        if (obj.hasOwnProperty(key)) {
                            if (typeof obj[key] === 'number') {
                                obj[key] = Math.round(obj[key] * Math.pow(10, precision)) / Math.pow(10, precision);
                            } else {
                                this.roundNumbers(obj[key], precision);
                            }
                        }
                    }
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        }

        const optimizer = new LottieOptimizer();
        let testResults = [];

        // List of available Lottie files (you would populate this dynamically)
        const lottieFiles = [
            'mob/output/1.json', 'mob/output/2.json', 'mob/output/3.json', 'mob/output/4.json', 'mob/output/5.json',
            'mob/output/6.json', 'mob/output/7.json', 'mob/output/8.json', 'mob/output/9.json', 'mob/output/10.json',
            'mob/output/11.json', 'mob/output/12.json', 'mob/output/13.json', 'mob/output/14.json', 'mob/output/15.json',
            'mob/output/16.json', 'mob/output/17.json', 'mob/output/18.json', 'mob/output/19.json', 'mob/output/20.json'
        ];

        async function runBatchTest() {
            testResults = [];
            const resultsContainer = document.getElementById('results');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            resultsContainer.innerHTML = '';
            
            for (let i = 0; i < lottieFiles.length; i++) {
                const fileName = lottieFiles[i];
                const progress = ((i + 1) / lottieFiles.length) * 100;
                
                progressBar.style.width = progress + '%';
                progressText.textContent = `Обработка ${fileName} (${i + 1}/${lottieFiles.length})`;
                
                try {
                    const response = await fetch(fileName);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    const lottieData = await response.json();
                    const result = await optimizer.optimizeLottie(lottieData);
                    
                    result.fileName = fileName;
                    testResults.push(result);
                    
                    displayResult(result);
                    
                } catch (error) {
                    console.error(`Error processing ${fileName}:`, error);
                    const errorResult = {
                        fileName: fileName,
                        error: error.message,
                        originalSize: 0,
                        optimizedSize: 0,
                        compressionRatio: 0
                    };
                    testResults.push(errorResult);
                    displayResult(errorResult);
                }
                
                // Small delay to prevent blocking the UI
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            progressText.textContent = `Завершено! Обработано ${lottieFiles.length} файлов`;
            displaySummary();
        }

        function displayResult(result) {
            const resultsContainer = document.getElementById('results');
            const resultCard = document.createElement('div');
            resultCard.className = 'result-card';
            
            if (result.error) {
                resultCard.innerHTML = `
                    <h3>${result.fileName}</h3>
                    <p style="color: red;">Ошибка: ${result.error}</p>
                `;
            } else {
                const compressionPercent = Math.round(result.compressionRatio * 100);
                const isGoodCompression = compressionPercent >= 60;
                
                resultCard.innerHTML = `
                    <h3>${result.fileName}</h3>
                    <div class="stats">
                        <span>Исходный: ${optimizer.formatFileSize(result.originalSize)}</span>
                        <span>Оптимизированный: ${optimizer.formatFileSize(result.optimizedSize)}</span>
                    </div>
                    <div class="stats">
                        <span>Сжатие: <strong style="color: ${isGoodCompression ? 'green' : 'orange'}">${compressionPercent}%</strong></span>
                        <span>Сэкономлено: ${optimizer.formatFileSize(result.originalSize - result.optimizedSize)}</span>
                    </div>
                    <button class="btn" onclick="downloadOptimized('${result.fileName}', ${testResults.length - 1})">
                        Скачать оптимизированный
                    </button>
                `;
            }
            
            resultsContainer.appendChild(resultCard);
        }

        function displaySummary() {
            const validResults = testResults.filter(r => !r.error);
            const totalOriginalSize = validResults.reduce((sum, r) => sum + r.originalSize, 0);
            const totalOptimizedSize = validResults.reduce((sum, r) => sum + r.optimizedSize, 0);
            const averageCompression = validResults.reduce((sum, r) => sum + r.compressionRatio, 0) / validResults.length;
            
            const summaryCard = document.createElement('div');
            summaryCard.className = 'test-container';
            summaryCard.innerHTML = `
                <h2>Сводка результатов</h2>
                <div class="test-results">
                    <div class="result-card">
                        <h4>Общая статистика</h4>
                        <p>Обработано файлов: ${validResults.length}</p>
                        <p>Ошибок: ${testResults.length - validResults.length}</p>
                        <p>Общий исходный размер: ${optimizer.formatFileSize(totalOriginalSize)}</p>
                        <p>Общий оптимизированный размер: ${optimizer.formatFileSize(totalOptimizedSize)}</p>
                        <p>Общая экономия: ${optimizer.formatFileSize(totalOriginalSize - totalOptimizedSize)}</p>
                        <p>Среднее сжатие: <strong>${Math.round(averageCompression * 100)}%</strong></p>
                    </div>
                </div>
            `;
            
            document.getElementById('results').appendChild(summaryCard);
        }

        function downloadOptimized(fileName, index) {
            const result = testResults[index];
            if (result && result.optimized) {
                const dataStr = JSON.stringify(result.optimized, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = fileName.replace('.json', '_optimized.json').replace('mob/output/', '');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('progressBar').style.width = '0%';
            document.getElementById('progressText').textContent = 'Готов к тестированию';
            testResults = [];
        }
    </script>
</body>
</html>
