{"name": "lottie-optimizer", "version": "1.0.0", "description": "Lottie JSON optimizer with image compression", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "serve": "node server.js"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "cors": "^2.8.5", "lottie-web": "^5.12.2"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/express": "^4.17.21", "@types/multer": "^1.4.11", "@types/cors": "^2.8.17", "@vitejs/plugin-react": "^4.2.1", "typescript": "^5.2.2", "vite": "^5.0.8"}}