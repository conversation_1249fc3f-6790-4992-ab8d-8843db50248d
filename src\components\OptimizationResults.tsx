import React, { useCallback } from 'react';

interface OptimizationResult {
  optimized: any;
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
}

interface OptimizationResultsProps {
  result: OptimizationResult;
  fileName: string;
  onReset: () => void;
}

export const OptimizationResults: React.FC<OptimizationResultsProps> = ({ 
  result, 
  fileName, 
  onReset 
}) => {
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  const downloadOptimized = useCallback(() => {
    const dataStr = JSON.stringify(result.optimized, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName.replace('.json', '_optimized.json');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [result.optimized, fileName]);

  const copyToClipboard = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(result.optimized));
      alert('JSON скопирован в буфер обмена!');
    } catch (err) {
      console.error('Ошибка копирования:', err);
      alert('Не удалось скопировать в буфер обмена');
    }
  }, [result.optimized]);

  const compressionPercentage = Math.round(result.compressionRatio * 100);
  const isGoodCompression = compressionPercentage >= 60;

  return (
    <div className="results">
      <h2>Результаты оптимизации</h2>
      
      <div className="stats">
        <div className="stat-card">
          <h3>{formatFileSize(result.originalSize)}</h3>
          <p>Исходный размер</p>
        </div>
        
        <div className="stat-card">
          <h3>{formatFileSize(result.optimizedSize)}</h3>
          <p>Оптимизированный размер</p>
        </div>
        
        <div className="stat-card">
          <h3>{compressionPercentage}%</h3>
          <p>Сжатие</p>
        </div>
        
        <div className="stat-card">
          <h3>{formatFileSize(result.originalSize - result.optimizedSize)}</h3>
          <p>Сэкономлено</p>
        </div>
      </div>

      {!isGoodCompression && (
        <div className="error">
          <strong>Внимание:</strong> Уровень сжатия ({compressionPercentage}%) ниже рекомендуемого порога в 60%.
          Возможно, файл уже был оптимизирован или содержит мало данных для сжатия.
        </div>
      )}

      {isGoodCompression && (
        <div className="success">
          <strong>Отлично!</strong> Файл успешно оптимизирован с сжатием {compressionPercentage}%.
        </div>
      )}

      <div className="actions">
        <button className="btn btn-primary" onClick={downloadOptimized}>
          📥 Скачать оптимизированный файл
        </button>
        
        <button className="btn btn-secondary" onClick={copyToClipboard}>
          📋 Копировать JSON
        </button>
        
        <button className="btn btn-secondary" onClick={onReset}>
          🔄 Оптимизировать другой файл
        </button>
      </div>

      <details style={{ marginTop: '20px' }}>
        <summary style={{ cursor: 'pointer', fontWeight: 'bold', marginBottom: '10px' }}>
          Просмотр оптимизированного JSON
        </summary>
        <pre style={{ 
          background: '#f8f9fa', 
          padding: '15px', 
          borderRadius: '8px', 
          overflow: 'auto',
          maxHeight: '400px',
          fontSize: '12px'
        }}>
          {JSON.stringify(result.optimized, null, 2)}
        </pre>
      </details>
    </div>
  );
};
